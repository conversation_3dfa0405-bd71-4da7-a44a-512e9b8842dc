#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية لاختبار النظام
"""

import sqlite3
import os

def create_test_data():
    """إنشاء بيانات تجريبية"""
    
    # حذف قاعدة البيانات القديمة إن وجدت
    if os.path.exists('attendance.db'):
        os.remove('attendance.db')
    
    # إنشاء قاعدة البيانات
    conn = sqlite3.connect('attendance.db')
    cur = conn.cursor()
    
    # إنشاء جدول الطلاب
    cur.execute('''
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            grade TEXT NOT NULL,
            section TEXT NOT NULL
        )
    ''')
    
    # إنشاء جدول الغياب
    cur.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER,
            student_name TEXT,
            date TEXT NOT NULL,
            status TEXT NOT NULL,
            FOREIGN KEY (student_id) REFERENCES students (id)
        )
    ''')
    
    # إنشاء جدول إعدادات المدرسة
    cur.execute('''
        CREATE TABLE IF NOT EXISTS school_settings (
            id INTEGER PRIMARY KEY,
            school_name TEXT,
            education_department TEXT,
            education_office TEXT,
            logo_path TEXT
        )
    ''')
    
    # إدراج بيانات تجريبية للطلاب
    students_data = [
        # الأول الابتدائي - أ
        ('أحمد محمد علي', 'الأول الابتدائي', 'أ'),
        ('فاطمة أحمد سالم', 'الأول الابتدائي', 'أ'),
        ('محمد عبدالله حسن', 'الأول الابتدائي', 'أ'),
        ('عائشة سعد محمد', 'الأول الابتدائي', 'أ'),
        ('علي حسن أحمد', 'الأول الابتدائي', 'أ'),
        ('خديجة عبدالرحمن', 'الأول الابتدائي', 'أ'),
        ('يوسف إبراهيم علي', 'الأول الابتدائي', 'أ'),
        ('مريم سالم حسن', 'الأول الابتدائي', 'أ'),
        ('عبدالرحمن محمد', 'الأول الابتدائي', 'أ'),
        ('زينب أحمد علي', 'الأول الابتدائي', 'أ'),
        
        # الأول الابتدائي - ب
        ('سارة محمد أحمد', 'الأول الابتدائي', 'ب'),
        ('حسام علي سعد', 'الأول الابتدائي', 'ب'),
        ('نور الهدى حسن', 'الأول الابتدائي', 'ب'),
        ('عمر عبدالله', 'الأول الابتدائي', 'ب'),
        ('هدى إبراهيم', 'الأول الابتدائي', 'ب'),
        
        # الثاني الابتدائي - أ
        ('خالد سعد محمد', 'الثاني الابتدائي', 'أ'),
        ('أسماء علي حسن', 'الثاني الابتدائي', 'أ'),
        ('طارق أحمد سالم', 'الثاني الابتدائي', 'أ'),
        ('رقية محمد علي', 'الثاني الابتدائي', 'أ'),
        ('سليم عبدالرحمن', 'الثاني الابتدائي', 'أ'),
        
        # الثالث الابتدائي - أ
        ('ياسر محمد حسن', 'الثالث الابتدائي', 'أ'),
        ('هالة أحمد سعد', 'الثالث الابتدائي', 'أ'),
        ('نادر علي محمد', 'الثالث الابتدائي', 'أ'),
        ('سلمى حسن أحمد', 'الثالث الابتدائي', 'أ'),
        ('وليد سالم علي', 'الثالث الابتدائي', 'أ'),
    ]
    
    cur.executemany('INSERT INTO students (name, grade, section) VALUES (?, ?, ?)', students_data)
    
    # إدراج إعدادات المدرسة التجريبية
    school_data = (
        1,
        'مدرسة الأمل الابتدائية',
        'الإدارة العامة للتعليم بمنطقة الرياض',
        'مكتب التعليم بشرق الرياض',
        ''
    )
    
    cur.execute('''
        INSERT INTO school_settings 
        (id, school_name, education_department, education_office, logo_path)
        VALUES (?, ?, ?, ?, ?)
    ''', school_data)
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء البيانات التجريبية بنجاح!")
    print("📊 البيانات المضافة:")
    print(f"   - {len(students_data)} طالب وطالبة")
    print("   - 3 مراحل دراسية")
    print("   - إعدادات مدرسة تجريبية")
    print("🚀 يمكنك الآن تشغيل النظام واختبار جميع الوظائف")

if __name__ == "__main__":
    create_test_data()
