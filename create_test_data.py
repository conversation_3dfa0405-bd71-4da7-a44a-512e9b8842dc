#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية لاختبار النظام
"""

import sqlite3
import os

def create_test_data():
    """إنشاء بيانات تجريبية"""
    
    # حذف قاعدة البيانات القديمة إن وجدت
    if os.path.exists('attendance.db'):
        os.remove('attendance.db')
    
    # إنشاء قاعدة البيانات
    conn = sqlite3.connect('attendance.db')
    cur = conn.cursor()
    
    # إنشاء جدول الطلاب
    cur.execute('''
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            national_id TEXT UNIQUE NOT NULL,
            grade TEXT NOT NULL,
            section TEXT NOT NULL,
            hijri_year TEXT NOT NULL,
            semester TEXT NOT NULL,
            created_date TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_date TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول الغياب
    cur.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER,
            student_name TEXT,
            national_id TEXT,
            date TEXT NOT NULL,
            hijri_date TEXT,
            hijri_year TEXT,
            semester TEXT,
            status TEXT NOT NULL,
            created_date TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_date TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id)
        )
    ''')
    
    # إنشاء جدول إعدادات المدرسة
    cur.execute('''
        CREATE TABLE IF NOT EXISTS school_settings (
            id INTEGER PRIMARY KEY,
            school_name TEXT,
            education_department TEXT,
            education_office TEXT,
            logo_path TEXT,
            current_hijri_year TEXT,
            current_semester TEXT,
            excel_import_path TEXT,
            last_import_date TEXT
        )
    ''')
    
    # إدراج بيانات تجريبية للطلاب
    students_data = [
        # الأول الابتدائي - أ
        ('أحمد محمد علي', '1234567890', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('فاطمة أحمد سالم', '1234567891', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('محمد عبدالله حسن', '1234567892', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('عائشة سعد محمد', '1234567893', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('علي حسن أحمد', '1234567894', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('خديجة عبدالرحمن', '1234567895', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('يوسف إبراهيم علي', '1234567896', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('مريم سالم حسن', '1234567897', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('عبدالرحمن محمد', '1234567898', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('زينب أحمد علي', '1234567899', 'الأول الابتدائي', 'أ', '1446', 'الفصل الأول'),

        # الأول الابتدائي - ب
        ('سارة محمد أحمد', '1234567900', 'الأول الابتدائي', 'ب', '1446', 'الفصل الأول'),
        ('حسام علي سعد', '1234567901', 'الأول الابتدائي', 'ب', '1446', 'الفصل الأول'),
        ('نور الهدى حسن', '1234567902', 'الأول الابتدائي', 'ب', '1446', 'الفصل الأول'),
        ('عمر عبدالله', '1234567903', 'الأول الابتدائي', 'ب', '1446', 'الفصل الأول'),
        ('هدى إبراهيم', '1234567904', 'الأول الابتدائي', 'ب', '1446', 'الفصل الأول'),

        # الثاني الابتدائي - أ
        ('خالد سعد محمد', '1234567905', 'الثاني الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('أسماء علي حسن', '1234567906', 'الثاني الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('طارق أحمد سالم', '1234567907', 'الثاني الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('رقية محمد علي', '1234567908', 'الثاني الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('سليم عبدالرحمن', '1234567909', 'الثاني الابتدائي', 'أ', '1446', 'الفصل الأول'),

        # الثالث الابتدائي - أ
        ('ياسر محمد حسن', '1234567910', 'الثالث الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('هالة أحمد سعد', '1234567911', 'الثالث الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('نادر علي محمد', '1234567912', 'الثالث الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('سلمى حسن أحمد', '1234567913', 'الثالث الابتدائي', 'أ', '1446', 'الفصل الأول'),
        ('وليد سالم علي', '1234567914', 'الثالث الابتدائي', 'أ', '1446', 'الفصل الأول'),
    ]

    cur.executemany('''
        INSERT INTO students (name, national_id, grade, section, hijri_year, semester)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', students_data)
    
    # إدراج إعدادات المدرسة التجريبية
    school_data = (
        1,
        'مدرسة الأمل الابتدائية',
        'الإدارة العامة للتعليم بمنطقة الرياض',
        'مكتب التعليم بشرق الرياض',
        '',
        '1446',
        'الفصل الأول',
        '',
        ''
    )

    cur.execute('''
        INSERT INTO school_settings
        (id, school_name, education_department, education_office, logo_path,
         current_hijri_year, current_semester, excel_import_path, last_import_date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', school_data)
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء البيانات التجريبية بنجاح!")
    print("📊 البيانات المضافة:")
    print(f"   - {len(students_data)} طالب وطالبة")
    print("   - 3 مراحل دراسية")
    print("   - إعدادات مدرسة تجريبية")
    print("🚀 يمكنك الآن تشغيل النظام واختبار جميع الوظائف")

if __name__ == "__main__":
    create_test_data()
