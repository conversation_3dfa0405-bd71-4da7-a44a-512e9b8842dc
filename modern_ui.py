#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة التصميم الحديث للنظام
تصميم بقسمين: قائمة جانبية + منطقة المحتوى
"""

import sys
from PySide6 import QtWidgets, QtCore, QtGui
from main import MainWindow
import datetime

class ModernMainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 نظام إدارة الغياب المدرسي - التصميم الحديث")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        
        # النافذة الرئيسية للتطبيق
        self.main_app = None
        
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QtWidgets.QHBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # القائمة الجانبية
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # منطقة المحتوى
        self.create_content_area()
        main_layout.addWidget(self.content_area)
        
        # تحديد النسب
        main_layout.setStretch(0, 1)  # القائمة الجانبية 25%
        main_layout.setStretch(1, 3)  # منطقة المحتوى 75%
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = QtWidgets.QFrame()
        self.sidebar.setFixedWidth(350)
        self.sidebar.setObjectName("sidebar")
        
        sidebar_layout = QtWidgets.QVBoxLayout(self.sidebar)
        sidebar_layout.setSpacing(15)
        sidebar_layout.setContentsMargins(20, 20, 20, 20)
        
        # شعار النظام
        logo_frame = QtWidgets.QFrame()
        logo_frame.setObjectName("logoFrame")
        logo_layout = QtWidgets.QVBoxLayout(logo_frame)
        
        logo_label = QtWidgets.QLabel("🎓")
        logo_label.setAlignment(QtCore.Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 48px; margin: 10px;")
        
        title_label = QtWidgets.QLabel("نظام إدارة الغياب")
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        
        subtitle_label = QtWidgets.QLabel("الإصدار المطور")
        subtitle_label.setAlignment(QtCore.Qt.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        
        logo_layout.addWidget(logo_label)
        logo_layout.addWidget(title_label)
        logo_layout.addWidget(subtitle_label)
        
        sidebar_layout.addWidget(logo_frame)
        
        # أزرار القائمة
        self.menu_buttons = []
        
        menu_items = [
            ("📋", "الغياب", "إدارة غياب الطلاب", "#8e44ad"),
            ("👤", "أمين المخزن", "إدارة المخزن والمواد", "#e67e22"),
            ("🏪", "المخزن", "عرض المخزن والمواد", "#3498db"),
            ("✏️", "الكتابة", "تسجيل البيانات", "#27ae60"),
            ("🛒", "المشرف", "إدارة الإشراف", "#e74c3c"),
            ("⚙️", "إعدادات النظام", "إعدادات عامة", "#95a5a6")
        ]
        
        for icon, title, desc, color in menu_items:
            btn = self.create_menu_button(icon, title, desc, color)
            self.menu_buttons.append(btn)
            sidebar_layout.addWidget(btn)
        
        # مساحة فارغة
        sidebar_layout.addStretch()
        
        # زر حول
        about_btn = self.create_menu_button("ℹ️", "حول", "معلومات البرنامج والمطور", "#34495e")
        about_btn.clicked.connect(self.show_about)
        sidebar_layout.addWidget(about_btn)
        
        # معلومات المستخدم
        user_frame = QtWidgets.QFrame()
        user_frame.setObjectName("userFrame")
        user_layout = QtWidgets.QVBoxLayout(user_frame)
        
        user_label = QtWidgets.QLabel("👤 المدير العام")
        user_label.setObjectName("userLabel")
        
        date_label = QtWidgets.QLabel(datetime.datetime.now().strftime("%Y/%m/%d"))
        date_label.setObjectName("dateLabel")
        
        user_layout.addWidget(user_label)
        user_layout.addWidget(date_label)
        
        sidebar_layout.addWidget(user_frame)
        
    def create_menu_button(self, icon, title, description, color):
        """إنشاء زر قائمة"""
        btn = QtWidgets.QPushButton()
        btn.setObjectName("menuButton")
        btn.setMinimumHeight(80)
        
        # تخطيط الزر
        layout = QtWidgets.QHBoxLayout(btn)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # الأيقونة
        icon_label = QtWidgets.QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color}; min-width: 40px;")
        icon_label.setAlignment(QtCore.Qt.AlignCenter)
        
        # النص
        text_widget = QtWidgets.QWidget()
        text_layout = QtWidgets.QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        title_label = QtWidgets.QLabel(title)
        title_label.setObjectName("menuButtonTitle")
        
        desc_label = QtWidgets.QLabel(description)
        desc_label.setObjectName("menuButtonDesc")
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        layout.addStretch()
        
        # ربط الأحداث
        if title == "الغياب":
            btn.clicked.connect(self.show_attendance)
        
        return btn
        
    def create_content_area(self):
        """إنشاء منطقة المحتوى"""
        self.content_area = QtWidgets.QFrame()
        self.content_area.setObjectName("contentArea")
        
        content_layout = QtWidgets.QVBoxLayout(self.content_area)
        content_layout.setContentsMargins(30, 30, 30, 30)
        
        # شريط العنوان
        header_frame = QtWidgets.QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QtWidgets.QHBoxLayout(header_frame)
        
        welcome_label = QtWidgets.QLabel("🏠 الصفحة الرئيسية")
        welcome_label.setObjectName("welcomeLabel")
        
        header_layout.addWidget(welcome_label)
        header_layout.addStretch()
        
        content_layout.addWidget(header_frame)
        
        # منطقة المحتوى الرئيسية
        self.main_content = QtWidgets.QStackedWidget()
        self.main_content.setObjectName("mainContent")
        
        # الصفحة الرئيسية
        self.create_home_page()
        
        content_layout.addWidget(self.main_content)
        
    def create_home_page(self):
        """إنشاء الصفحة الرئيسية"""
        home_page = QtWidgets.QWidget()
        home_layout = QtWidgets.QVBoxLayout(home_page)
        home_layout.setSpacing(30)
        
        # رسالة ترحيب
        welcome_frame = QtWidgets.QFrame()
        welcome_frame.setObjectName("welcomeFrame")
        welcome_layout = QtWidgets.QVBoxLayout(welcome_frame)
        
        welcome_title = QtWidgets.QLabel("مرحباً بك في نظام إدارة الغياب المدرسي")
        welcome_title.setObjectName("welcomeTitle")
        welcome_title.setAlignment(QtCore.Qt.AlignCenter)
        
        welcome_desc = QtWidgets.QLabel("نظام شامل لإدارة غياب الطلاب مع تقارير متقدمة وإحصائيات دقيقة")
        welcome_desc.setObjectName("welcomeDesc")
        welcome_desc.setAlignment(QtCore.Qt.AlignCenter)
        welcome_desc.setWordWrap(True)
        
        welcome_layout.addWidget(welcome_title)
        welcome_layout.addWidget(welcome_desc)
        
        home_layout.addWidget(welcome_frame)
        
        # بطاقات الميزات
        features_frame = QtWidgets.QFrame()
        features_layout = QtWidgets.QGridLayout(features_frame)
        features_layout.setSpacing(20)
        
        features = [
            ("📊", "تقارير متقدمة", "تقارير شاملة مع إحصائيات دقيقة"),
            ("💾", "حفظ متعدد", "حفظ بصيغ PDF, Word, Excel"),
            ("🖨️", "طباعة احترافية", "طباعة مع معاينة متقدمة"),
            ("⚙️", "إعدادات مرنة", "تخصيص كامل للنظام")
        ]
        
        for i, (icon, title, desc) in enumerate(features):
            card = self.create_feature_card(icon, title, desc)
            row, col = divmod(i, 2)
            features_layout.addWidget(card, row, col)
        
        home_layout.addWidget(features_frame)
        home_layout.addStretch()
        
        self.main_content.addWidget(home_page)
        
    def create_feature_card(self, icon, title, description):
        """إنشاء بطاقة ميزة"""
        card = QtWidgets.QFrame()
        card.setObjectName("featureCard")
        card.setMinimumHeight(120)
        
        layout = QtWidgets.QVBoxLayout(card)
        layout.setAlignment(QtCore.Qt.AlignCenter)
        layout.setSpacing(10)
        
        icon_label = QtWidgets.QLabel(icon)
        icon_label.setAlignment(QtCore.Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; margin: 5px;")
        
        title_label = QtWidgets.QLabel(title)
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        title_label.setObjectName("featureTitle")
        
        desc_label = QtWidgets.QLabel(description)
        desc_label.setAlignment(QtCore.Qt.AlignCenter)
        desc_label.setObjectName("featureDesc")
        desc_label.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return card
        
    def show_attendance(self):
        """عرض نافذة الغياب"""
        if not self.main_app:
            self.main_app = MainWindow()

        self.main_app.show()
        self.main_app.raise_()
        self.main_app.activateWindow()
        
    def show_about(self):
        """عرض نافذة حول"""
        about_dialog = AboutDialog(self)
        about_dialog.exec()

    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            /* النافذة الرئيسية */
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }

            /* القائمة الجانبية */
            #sidebar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border-right: 3px solid #3498db;
            }

            /* إطار الشعار */
            #logoFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 20px;
            }

            #titleLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                margin: 5px;
            }

            #subtitleLabel {
                color: #bdc3c7;
                font-size: 12px;
                margin: 5px;
            }

            /* أزرار القائمة */
            #menuButton {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid transparent;
                border-radius: 12px;
                padding: 10px;
                text-align: right;
                margin: 2px;
            }

            #menuButton:hover {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid #3498db;
                transform: translateX(-5px);
            }

            #menuButton:pressed {
                background: rgba(255, 255, 255, 0.3);
            }

            #menuButtonTitle {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }

            #menuButtonDesc {
                color: #bdc3c7;
                font-size: 11px;
            }

            /* إطار المستخدم */
            #userFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 15px;
                margin-top: 10px;
            }

            #userLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }

            #dateLabel {
                color: #bdc3c7;
                font-size: 12px;
            }

            /* منطقة المحتوى */
            #contentArea {
                background: white;
                border-radius: 0px 15px 15px 0px;
                margin: 10px 0px 10px 0px;
            }

            /* شريط العنوان */
            #headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 20px;
            }

            #welcomeLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
            }

            /* المحتوى الرئيسي */
            #mainContent {
                background: transparent;
            }

            /* إطار الترحيب */
            #welcomeFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 40px;
                margin: 20px;
            }

            #welcomeTitle {
                color: white;
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 15px;
            }

            #welcomeDesc {
                color: rgba(255, 255, 255, 0.9);
                font-size: 16px;
                line-height: 1.6;
            }

            /* بطاقات الميزات */
            #featureCard {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid #e9ecef;
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
            }

            #featureCard:hover {
                border: 2px solid #3498db;
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
            }

            #featureTitle {
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                margin: 8px 0;
            }

            #featureDesc {
                color: #7f8c8d;
                font-size: 13px;
                line-height: 1.4;
            }
        """)


class AboutDialog(QtWidgets.QDialog):
    """نافذة حول البرنامج"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("حول البرنامج")
        self.setFixedSize(500, 600)
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QtWidgets.QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # شعار البرنامج
        logo_label = QtWidgets.QLabel("🎓")
        logo_label.setAlignment(QtCore.Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 64px; margin: 20px;")
        layout.addWidget(logo_label)

        # اسم البرنامج
        title_label = QtWidgets.QLabel("نظام إدارة الغياب المدرسي")
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        title_label.setObjectName("aboutTitle")
        layout.addWidget(title_label)

        # الإصدار
        version_label = QtWidgets.QLabel("الإصدار 2.0 - التصميم المطور")
        version_label.setAlignment(QtCore.Qt.AlignCenter)
        version_label.setObjectName("aboutVersion")
        layout.addWidget(version_label)

        # خط فاصل
        line = QtWidgets.QFrame()
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(line)

        # معلومات المطور
        dev_frame = QtWidgets.QFrame()
        dev_frame.setObjectName("devFrame")
        dev_layout = QtWidgets.QVBoxLayout(dev_frame)

        dev_title = QtWidgets.QLabel("👨‍💻 معلومات المطور")
        dev_title.setObjectName("sectionTitle")
        dev_layout.addWidget(dev_title)

        dev_info = QtWidgets.QLabel("""
        <b>المطور:</b> Ahmed Rabie<br>
        <b>الواتساب:</b> 00201154013932<br>
        <b>البريد الإلكتروني:</b> <EMAIL><br>
        <b>التخصص:</b> تطوير أنظمة إدارة المدارس
        """)
        dev_info.setObjectName("infoText")
        dev_layout.addWidget(dev_info)

        layout.addWidget(dev_frame)

        # ميزات البرنامج
        features_frame = QtWidgets.QFrame()
        features_frame.setObjectName("featuresFrame")
        features_layout = QtWidgets.QVBoxLayout(features_frame)

        features_title = QtWidgets.QLabel("✨ ميزات البرنامج")
        features_title.setObjectName("sectionTitle")
        features_layout.addWidget(features_title)

        features_list = QtWidgets.QLabel("""
        • إدارة شاملة لغياب الطلاب
        • تقارير متقدمة مع إحصائيات دقيقة
        • حفظ بصيغ متعددة (PDF, Word, Excel)
        • طباعة احترافية مع معاينة
        • واجهة مستخدم عصرية وسهلة
        • دعم التاريخ الهجري والميلادي
        • إعدادات مرنة للمدرسة
        """)
        features_list.setObjectName("infoText")
        features_layout.addWidget(features_list)

        layout.addWidget(features_frame)

        # أزرار التواصل
        contact_layout = QtWidgets.QHBoxLayout()

        whatsapp_btn = QtWidgets.QPushButton("📱 واتساب")
        whatsapp_btn.setObjectName("contactBtn")
        whatsapp_btn.clicked.connect(self.open_whatsapp)

        email_btn = QtWidgets.QPushButton("📧 إيميل")
        email_btn.setObjectName("contactBtn")
        email_btn.clicked.connect(self.open_email)

        contact_layout.addWidget(whatsapp_btn)
        contact_layout.addWidget(email_btn)

        layout.addLayout(contact_layout)

        # زر إغلاق
        close_btn = QtWidgets.QPushButton("إغلاق")
        close_btn.setObjectName("closeBtn")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

    def open_whatsapp(self):
        """فتح واتساب"""
        import webbrowser
        webbrowser.open("https://wa.me/00201154013932")

    def open_email(self):
        """فتح البريد الإلكتروني"""
        import webbrowser
        webbrowser.open("mailto:<EMAIL>")

    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }

            #aboutTitle {
                color: white;
                font-size: 24px;
                font-weight: bold;
                margin: 10px;
            }

            #aboutVersion {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                margin: 5px;
            }

            #devFrame, #featuresFrame {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 10px;
                padding: 20px;
                margin: 10px 0;
            }

            #sectionTitle {
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }

            #infoText {
                color: #34495e;
                font-size: 13px;
                line-height: 1.6;
            }

            #contactBtn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }

            #contactBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }

            #closeBtn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 30px;
                font-weight: bold;
                font-size: 14px;
                margin-top: 10px;
            }

            #closeBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ec7063, stop:1 #e74c3c);
            }
        """)


if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)

    # تطبيق خط عربي
    font = QtGui.QFont("Segoe UI", 10)
    app.setFont(font)

    window = ModernMainWindow()
    window.show()

    sys.exit(app.exec())
