#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة التصميم الحديث للنظام
تصميم بقسمين: قائمة جانبية + منطقة المحتوى
"""

import sys
from PySide6 import QtWidgets, QtCore, QtGui
from main import MainWindow
import datetime

class ModernMainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 نظام إدارة الغياب المدرسي - التصميم الحديث")

        # تعيين حجم النافذة متوازن ووضعها في الوسط
        screen = QtWidgets.QApplication.primaryScreen().geometry()
        # حساب حجم متوازن (نسبة 16:10 مناسبة للشاشات الحديثة)
        width = int(screen.width() * 0.75)   # 75% من عرض الشاشة
        height = int(screen.height() * 0.7)  # 70% من ارتفاع الشاشة

        # التأكد من أن النافذة لا تتجاوز حدود معقولة
        max_width = 1200
        max_height = 800
        width = min(width, max_width)
        height = min(height, max_height)

        # وضع النافذة في الوسط
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)

        self.setLayoutDirection(QtCore.Qt.RightToLeft)

        # إنشاء مكونات النظام الداخلية
        self.setup_internal_components()

        self.setup_ui()
        self.apply_styles()

    def setup_internal_components(self):
        """إعداد المكونات الداخلية للنظام"""
        # استيراد الوظائف المطلوبة من النظام الرئيسي
        from main import get_connection, init_db, ensure_plain_db

        # تهيئة قاعدة البيانات
        ensure_plain_db()
        init_db()

        # متغيرات النظام
        self.current_students = []
        self.current_grade = ""
        self.current_section = ""
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QtWidgets.QHBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # منطقة المحتوى (في الوسط)
        self.create_content_area()
        main_layout.addWidget(self.content_area)

        # القائمة الجانبية (في اليمين)
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)

        # تحديد النسب
        main_layout.setStretch(0, 3)  # منطقة المحتوى 75%
        main_layout.setStretch(1, 1)  # القائمة الجانبية 25%
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = QtWidgets.QFrame()
        self.sidebar.setFixedWidth(350)
        self.sidebar.setObjectName("sidebar")
        
        sidebar_layout = QtWidgets.QVBoxLayout(self.sidebar)
        sidebar_layout.setSpacing(15)
        sidebar_layout.setContentsMargins(20, 20, 20, 20)
        
        # شعار النظام
        logo_frame = QtWidgets.QFrame()
        logo_frame.setObjectName("logoFrame")
        logo_layout = QtWidgets.QVBoxLayout(logo_frame)
        
        logo_label = QtWidgets.QLabel("🎓")
        logo_label.setAlignment(QtCore.Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 48px; margin: 10px;")
        
        title_label = QtWidgets.QLabel("نظام إدارة الغياب")
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        
        subtitle_label = QtWidgets.QLabel("الإصدار المطور")
        subtitle_label.setAlignment(QtCore.Qt.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        
        logo_layout.addWidget(logo_label)
        logo_layout.addWidget(title_label)
        logo_layout.addWidget(subtitle_label)
        
        sidebar_layout.addWidget(logo_frame)
        
        # أزرار القائمة
        self.menu_buttons = []

        menu_items = [
            ("📋", "تسجيل الغياب", "تسجيل غياب الطلاب اليومي", "#8e44ad"),
            ("👤", "تقرير طالب", "تقرير غياب طالب محدد", "#e67e22"),
            ("🏫", "تقرير فصل", "تقرير غياب فصل كامل", "#3498db"),
            ("🏢", "تقرير مدرسة", "تقرير غياب المدرسة", "#27ae60"),
            ("📊", "تقارير متقدمة", "تقارير وإحصائيات شاملة", "#e74c3c"),
            ("⚙️", "إعدادات المدرسة", "إعدادات بيانات المدرسة", "#95a5a6")
        ]
        
        for icon, title, desc, color in menu_items:
            btn = self.create_menu_button(icon, title, desc, color)
            # ربط كل زر بوظيفته المناسبة
            if title == "تسجيل الغياب":
                btn.clicked.connect(self.show_attendance_page)
            elif title == "تقرير طالب":
                btn.clicked.connect(self.show_student_report)
            elif title == "تقرير فصل":
                btn.clicked.connect(self.show_class_report)
            elif title == "تقرير مدرسة":
                btn.clicked.connect(self.show_school_report)
            elif title == "تقارير متقدمة":
                btn.clicked.connect(self.show_advanced_reports)
            elif title == "إعدادات المدرسة":
                btn.clicked.connect(self.show_school_settings)

            self.menu_buttons.append(btn)
            sidebar_layout.addWidget(btn)
        
        # مساحة فارغة
        sidebar_layout.addStretch()
        
        # زر حول
        about_btn = self.create_menu_button("ℹ️", "حول", "معلومات البرنامج والمطور", "#34495e")
        about_btn.clicked.connect(self.show_about)
        sidebar_layout.addWidget(about_btn)
        
        # معلومات المستخدم
        user_frame = QtWidgets.QFrame()
        user_frame.setObjectName("userFrame")
        user_layout = QtWidgets.QVBoxLayout(user_frame)
        
        user_label = QtWidgets.QLabel("👤 المدير العام")
        user_label.setObjectName("userLabel")
        
        date_label = QtWidgets.QLabel(datetime.datetime.now().strftime("%Y/%m/%d"))
        date_label.setObjectName("dateLabel")
        
        user_layout.addWidget(user_label)
        user_layout.addWidget(date_label)
        
        sidebar_layout.addWidget(user_frame)
        
    def create_menu_button(self, icon, title, description, color):
        """إنشاء زر قائمة"""
        btn = QtWidgets.QPushButton()
        btn.setObjectName("menuButton")
        btn.setMinimumHeight(80)
        
        # تخطيط الزر
        layout = QtWidgets.QHBoxLayout(btn)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # الأيقونة
        icon_label = QtWidgets.QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color}; min-width: 40px;")
        icon_label.setAlignment(QtCore.Qt.AlignCenter)
        
        # النص
        text_widget = QtWidgets.QWidget()
        text_layout = QtWidgets.QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        title_label = QtWidgets.QLabel(title)
        title_label.setObjectName("menuButtonTitle")
        
        desc_label = QtWidgets.QLabel(description)
        desc_label.setObjectName("menuButtonDesc")
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        layout.addStretch()

        return btn
        
    def create_content_area(self):
        """إنشاء منطقة المحتوى"""
        self.content_area = QtWidgets.QFrame()
        self.content_area.setObjectName("contentArea")
        
        content_layout = QtWidgets.QVBoxLayout(self.content_area)
        content_layout.setContentsMargins(30, 30, 30, 30)
        
        # شريط العنوان
        header_frame = QtWidgets.QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QtWidgets.QHBoxLayout(header_frame)
        
        welcome_label = QtWidgets.QLabel("🏠 الصفحة الرئيسية")
        welcome_label.setObjectName("welcomeLabel")
        
        header_layout.addWidget(welcome_label)
        header_layout.addStretch()
        
        content_layout.addWidget(header_frame)
        
        # منطقة المحتوى الرئيسية
        self.main_content = QtWidgets.QStackedWidget()
        self.main_content.setObjectName("mainContent")

        # إضافة صفحات مختلفة
        self.create_home_page()           # 0 - الصفحة الرئيسية
        self.create_attendance_page()     # 1 - تسجيل الغياب
        self.create_student_report_page() # 2 - تقرير طالب
        self.create_class_report_page()   # 3 - تقرير فصل
        self.create_school_report_page()  # 4 - تقرير مدرسة
        self.create_advanced_reports_page() # 5 - تقارير متقدمة
        self.create_settings_page()      # 6 - إعدادات المدرسة

        content_layout.addWidget(self.main_content)
        
    def create_home_page(self):
        """إنشاء الصفحة الرئيسية"""
        home_page = QtWidgets.QWidget()
        home_layout = QtWidgets.QVBoxLayout(home_page)
        home_layout.setSpacing(30)
        
        # رسالة ترحيب
        welcome_frame = QtWidgets.QFrame()
        welcome_frame.setObjectName("welcomeFrame")
        welcome_layout = QtWidgets.QVBoxLayout(welcome_frame)
        
        welcome_title = QtWidgets.QLabel("مرحباً بك في نظام إدارة الغياب المدرسي")
        welcome_title.setObjectName("welcomeTitle")
        welcome_title.setAlignment(QtCore.Qt.AlignCenter)
        
        welcome_desc = QtWidgets.QLabel("نظام شامل لإدارة غياب الطلاب مع تقارير متقدمة وإحصائيات دقيقة")
        welcome_desc.setObjectName("welcomeDesc")
        welcome_desc.setAlignment(QtCore.Qt.AlignCenter)
        welcome_desc.setWordWrap(True)
        
        welcome_layout.addWidget(welcome_title)
        welcome_layout.addWidget(welcome_desc)
        
        home_layout.addWidget(welcome_frame)
        
        # بطاقات الميزات
        features_frame = QtWidgets.QFrame()
        features_layout = QtWidgets.QGridLayout(features_frame)
        features_layout.setSpacing(20)
        
        features = [
            ("📊", "تقارير متقدمة", "تقارير شاملة مع إحصائيات دقيقة"),
            ("💾", "حفظ متعدد", "حفظ بصيغ PDF, Word, Excel"),
            ("🖨️", "طباعة احترافية", "طباعة مع معاينة متقدمة"),
            ("⚙️", "إعدادات مرنة", "تخصيص كامل للنظام")
        ]
        
        for i, (icon, title, desc) in enumerate(features):
            card = self.create_feature_card(icon, title, desc)
            row, col = divmod(i, 2)
            features_layout.addWidget(card, row, col)
        
        home_layout.addWidget(features_frame)
        home_layout.addStretch()
        
        self.main_content.addWidget(home_page)
        
    def create_feature_card(self, icon, title, description):
        """إنشاء بطاقة ميزة"""
        card = QtWidgets.QFrame()
        card.setObjectName("featureCard")
        card.setMinimumHeight(120)
        
        layout = QtWidgets.QVBoxLayout(card)
        layout.setAlignment(QtCore.Qt.AlignCenter)
        layout.setSpacing(10)
        
        icon_label = QtWidgets.QLabel(icon)
        icon_label.setAlignment(QtCore.Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; margin: 5px;")
        
        title_label = QtWidgets.QLabel(title)
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        title_label.setObjectName("featureTitle")
        
        desc_label = QtWidgets.QLabel(description)
        desc_label.setAlignment(QtCore.Qt.AlignCenter)
        desc_label.setObjectName("featureDesc")
        desc_label.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return card

    def create_attendance_page(self):
        """إنشاء صفحة تسجيل الغياب"""
        attendance_page = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(attendance_page)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان الصفحة
        title_label = QtWidgets.QLabel("📋 تسجيل الغياب اليومي")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title_label)

        # قسم اختيار الفصل
        class_frame = QtWidgets.QFrame()
        class_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        class_layout = QtWidgets.QGridLayout(class_frame)

        # المرحلة
        class_layout.addWidget(QtWidgets.QLabel('📚 المرحلة:'), 0, 0)
        self.cb_grade = QtWidgets.QComboBox()
        self.cb_grade.addItems(['الأول الابتدائي', 'الثاني الابتدائي', 'الثالث الابتدائي',
                               'الرابع الابتدائي', 'الخامس الابتدائي', 'السادس الابتدائي'])
        self.cb_grade.setMinimumHeight(35)
        class_layout.addWidget(self.cb_grade, 0, 1)

        # الفصل
        class_layout.addWidget(QtWidgets.QLabel('🎯 الفصل:'), 0, 2)
        self.cb_section = QtWidgets.QComboBox()
        self.cb_section.addItems(['أ', 'ب', 'ج', 'د', 'هـ'])
        self.cb_section.setMinimumHeight(35)
        class_layout.addWidget(self.cb_section, 0, 3)

        # زر تحميل الطلاب
        btn_load = QtWidgets.QPushButton('📂 تحميل الطلاب')
        btn_load.setMinimumHeight(35)
        btn_load.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34ce57, stop:1 #28a745);
            }
        """)
        btn_load.clicked.connect(self.load_students_internal)
        class_layout.addWidget(btn_load, 0, 4)

        layout.addWidget(class_frame)

        # قسم التاريخ والإعدادات
        date_frame = QtWidgets.QFrame()
        date_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        date_layout = QtWidgets.QGridLayout(date_frame)

        # التاريخ الميلادي
        date_layout.addWidget(QtWidgets.QLabel('📆 التاريخ:'), 0, 0)
        self.date_edit = QtWidgets.QDateEdit(QtCore.QDate.currentDate())
        self.date_edit.setDisplayFormat('yyyy/MM/dd')
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setMinimumHeight(35)
        self.date_edit.dateChanged.connect(self.update_hijri_date)
        date_layout.addWidget(self.date_edit, 0, 1)

        # التاريخ الهجري
        date_layout.addWidget(QtWidgets.QLabel('📅 التاريخ الهجري:'), 0, 2)
        self.lbl_hijri = QtWidgets.QLabel('سيتم حسابه تلقائياً')
        self.lbl_hijri.setStyleSheet("color: #6c757d; font-size: 12px; min-width: 150px;")
        date_layout.addWidget(self.lbl_hijri, 0, 3)

        # العام الهجري
        date_layout.addWidget(QtWidgets.QLabel('🗓️ العام الهجري:'), 1, 0)
        self.cb_hijri_year = QtWidgets.QComboBox()
        self.cb_hijri_year.addItems(['1445', '1446', '1447', '1448'])
        self.cb_hijri_year.setCurrentText('1446')
        self.cb_hijri_year.setMinimumHeight(35)
        date_layout.addWidget(self.cb_hijri_year, 1, 1)

        # الفصل الدراسي
        date_layout.addWidget(QtWidgets.QLabel('📚 الفصل الدراسي:'), 1, 2)
        self.cb_semester = QtWidgets.QComboBox()
        self.cb_semester.addItems(['الفصل الأول', 'الفصل الثاني', 'الفصل الثالث'])
        self.cb_semester.setMinimumHeight(35)
        date_layout.addWidget(self.cb_semester, 1, 3)

        layout.addWidget(date_frame)

        # تحديث التاريخ الهجري عند بدء التشغيل
        self.update_hijri_date()

        # جدول الطلاب
        self.students_table = QtWidgets.QTableWidget()
        self.students_table.setColumnCount(5)
        self.students_table.setHorizontalHeaderLabels(['غائب', 'الرقم', 'اسم الطالب', 'السجل المدني', 'نسبة الغياب'])
        self.students_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #dee2e6;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            QHeaderView::section {
                background-color: #6c757d;
                color: white;
                padding: 8px;
                font-weight: bold;
                border: none;
            }
        """)
        layout.addWidget(self.students_table)

        # أزرار العمليات
        buttons_frame = QtWidgets.QFrame()
        buttons_layout = QtWidgets.QHBoxLayout(buttons_frame)

        # زر حفظ الغياب
        btn_save = QtWidgets.QPushButton('💾 حفظ الغياب')
        btn_save.setMinimumHeight(40)
        btn_save.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3395ff, stop:1 #007bff);
            }
        """)
        btn_save.clicked.connect(self.save_attendance_internal)
        buttons_layout.addWidget(btn_save)

        buttons_layout.addStretch()
        layout.addWidget(buttons_frame)

        self.main_content.addWidget(attendance_page)

    def create_student_report_page(self):
        """إنشاء صفحة تقرير طالب"""
        page = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(page)

        title = QtWidgets.QLabel("👤 تقرير غياب طالب")
        title.setStyleSheet(self.get_page_title_style())
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        content = QtWidgets.QLabel("سيتم عرض تقرير الطالب هنا")
        content.setAlignment(QtCore.Qt.AlignCenter)
        content.setStyleSheet("font-size: 16px; color: #6c757d; padding: 50px;")
        layout.addWidget(content)

        self.main_content.addWidget(page)

    def create_class_report_page(self):
        """إنشاء صفحة تقرير فصل"""
        page = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(page)

        title = QtWidgets.QLabel("🏫 تقرير غياب فصل")
        title.setStyleSheet(self.get_page_title_style())
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        content = QtWidgets.QLabel("سيتم عرض تقرير الفصل هنا")
        content.setAlignment(QtCore.Qt.AlignCenter)
        content.setStyleSheet("font-size: 16px; color: #6c757d; padding: 50px;")
        layout.addWidget(content)

        self.main_content.addWidget(page)

    def create_school_report_page(self):
        """إنشاء صفحة تقرير مدرسة"""
        page = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(page)

        title = QtWidgets.QLabel("🏢 تقرير غياب المدرسة")
        title.setStyleSheet(self.get_page_title_style())
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        content = QtWidgets.QLabel("سيتم عرض تقرير المدرسة هنا")
        content.setAlignment(QtCore.Qt.AlignCenter)
        content.setStyleSheet("font-size: 16px; color: #6c757d; padding: 50px;")
        layout.addWidget(content)

        self.main_content.addWidget(page)

    def create_advanced_reports_page(self):
        """إنشاء صفحة التقارير المتقدمة"""
        page = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(page)

        title = QtWidgets.QLabel("📊 التقارير المتقدمة والإحصائيات")
        title.setStyleSheet(self.get_page_title_style())
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        content = QtWidgets.QLabel("سيتم عرض التقارير المتقدمة والإحصائيات هنا")
        content.setAlignment(QtCore.Qt.AlignCenter)
        content.setStyleSheet("font-size: 16px; color: #6c757d; padding: 50px;")
        layout.addWidget(content)

        self.main_content.addWidget(page)

    def create_settings_page(self):
        """إنشاء صفحة إعدادات المدرسة"""
        page = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(page)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان الصفحة
        title = QtWidgets.QLabel("⚙️ إعدادات بيانات المدرسة")
        title.setStyleSheet(self.get_page_title_style())
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        # قسم بيانات المدرسة
        school_frame = QtWidgets.QFrame()
        school_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        school_layout = QtWidgets.QFormLayout(school_frame)

        # اسم المدرسة
        self.school_name_edit = QtWidgets.QLineEdit()
        self.school_name_edit.setPlaceholderText("أدخل اسم المدرسة")
        self.school_name_edit.setMinimumHeight(35)
        school_layout.addRow("🏫 اسم المدرسة:", self.school_name_edit)

        # الإدارة العامة
        self.education_dept_edit = QtWidgets.QLineEdit()
        self.education_dept_edit.setPlaceholderText("أدخل اسم الإدارة العامة للتعليم")
        self.education_dept_edit.setMinimumHeight(35)
        school_layout.addRow("🏛️ الإدارة العامة:", self.education_dept_edit)

        # مكتب التعليم
        self.education_office_edit = QtWidgets.QLineEdit()
        self.education_office_edit.setPlaceholderText("أدخل اسم مكتب التعليم")
        self.education_office_edit.setMinimumHeight(35)
        school_layout.addRow("🏢 مكتب التعليم:", self.education_office_edit)

        # العام الهجري الحالي
        self.current_hijri_year_edit = QtWidgets.QComboBox()
        self.current_hijri_year_edit.addItems(['1445', '1446', '1447', '1448'])
        self.current_hijri_year_edit.setMinimumHeight(35)
        school_layout.addRow("📅 العام الهجري الحالي:", self.current_hijri_year_edit)

        # الفصل الدراسي الحالي
        self.current_semester_edit = QtWidgets.QComboBox()
        self.current_semester_edit.addItems(['الفصل الأول', 'الفصل الثاني', 'الفصل الثالث'])
        self.current_semester_edit.setMinimumHeight(35)
        school_layout.addRow("📚 الفصل الدراسي الحالي:", self.current_semester_edit)

        layout.addWidget(school_frame)

        # قسم استيراد البيانات
        import_frame = QtWidgets.QFrame()
        import_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        import_layout = QtWidgets.QVBoxLayout(import_frame)

        import_title = QtWidgets.QLabel("📂 استيراد بيانات الطلاب من Excel")
        import_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        import_layout.addWidget(import_title)

        # مسار ملف Excel
        file_layout = QtWidgets.QHBoxLayout()
        self.excel_path_edit = QtWidgets.QLineEdit()
        self.excel_path_edit.setPlaceholderText("اختر ملف Excel للاستيراد")
        self.excel_path_edit.setMinimumHeight(35)
        file_layout.addWidget(self.excel_path_edit)

        browse_btn = QtWidgets.QPushButton("📁 تصفح")
        browse_btn.setMinimumHeight(35)
        browse_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        browse_btn.clicked.connect(self.browse_excel_file)
        file_layout.addWidget(browse_btn)

        import_layout.addLayout(file_layout)

        # زر استيراد البيانات
        import_btn = QtWidgets.QPushButton("📥 استيراد البيانات")
        import_btn.setMinimumHeight(40)
        import_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
                margin-top: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34ce57, stop:1 #28a745);
            }
        """)
        import_btn.clicked.connect(self.import_excel_data)
        import_layout.addWidget(import_btn)

        layout.addWidget(import_frame)

        # أزرار الحفظ
        buttons_layout = QtWidgets.QHBoxLayout()

        save_btn = QtWidgets.QPushButton("💾 حفظ الإعدادات")
        save_btn.setMinimumHeight(40)
        save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3395ff, stop:1 #007bff);
            }
        """)
        save_btn.clicked.connect(self.save_school_settings)
        buttons_layout.addWidget(save_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # تحميل الإعدادات الحالية
        self.load_current_school_settings()

        self.main_content.addWidget(page)

    def get_page_title_style(self):
        """الحصول على نمط عنوان الصفحة"""
        return """
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """

    def show_attendance_page(self):
        """عرض صفحة تسجيل الغياب"""
        self.main_content.setCurrentIndex(1)

    def show_student_report(self):
        """عرض تقرير طالب"""
        self.main_content.setCurrentIndex(2)

    def show_class_report(self):
        """عرض تقرير فصل"""
        self.main_content.setCurrentIndex(3)

    def show_school_report(self):
        """عرض تقرير مدرسة"""
        self.main_content.setCurrentIndex(4)

    def show_advanced_reports(self):
        """عرض التقارير المتقدمة"""
        self.main_content.setCurrentIndex(5)

    def show_school_settings(self):
        """عرض إعدادات المدرسة"""
        self.main_content.setCurrentIndex(6)

    def update_hijri_date(self):
        """تحديث التاريخ الهجري"""
        try:
            from hijri_converter import Gregorian

            date = self.date_edit.date()
            g = Gregorian(date.year(), date.month(), date.day()).to_hijri()
            hijri_text = f'{g.day:02d}/{g.month:02d}/{g.year} هـ'
            self.lbl_hijri.setText(hijri_text)
        except ImportError:
            self.lbl_hijri.setText('مكتبة التاريخ الهجري غير متوفرة')
        except Exception:
            self.lbl_hijri.setText('خطأ في حساب التاريخ')

    def load_students_internal(self):
        """تحميل الطلاب داخلياً"""
        try:
            from main import get_connection

            grade = self.cb_grade.currentText()
            section = self.cb_section.currentText()

            conn = get_connection()
            cur = conn.cursor()

            # جلب الطلاب مع السجل المدني
            cur.execute('''
                SELECT id, name, national_id, hijri_year, semester
                FROM students
                WHERE grade=? AND section=?
                ORDER BY name
            ''', (grade, section))
            rows = cur.fetchall()

            if not rows:
                QtWidgets.QMessageBox.information(
                    self, 'تنبيه',
                    f'لا يوجد طلاب في {grade} - {section}\n'
                    'يرجى استيراد الطلاب أولاً من النظام الرئيسي'
                )
                conn.close()
                return

            # إعداد الجدول
            self.students_table.setRowCount(len(rows))

            for i, (student_id, name, national_id, hijri_year, semester) in enumerate(rows):
                # عمود الغياب (checkbox)
                checkbox = QtWidgets.QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: 10px; }")
                self.students_table.setCellWidget(i, 0, checkbox)

                # رقم الطالب
                id_item = QtWidgets.QTableWidgetItem(str(student_id))
                id_item.setData(QtCore.Qt.UserRole, student_id)
                id_item.setTextAlignment(QtCore.Qt.AlignCenter)
                self.students_table.setItem(i, 1, id_item)

                # اسم الطالب
                name_item = QtWidgets.QTableWidgetItem(name)
                name_item.setData(QtCore.Qt.UserRole, national_id)  # حفظ السجل المدني
                name_item.setTextAlignment(QtCore.Qt.AlignCenter)
                self.students_table.setItem(i, 2, name_item)

                # السجل المدني
                national_item = QtWidgets.QTableWidgetItem(national_id)
                national_item.setTextAlignment(QtCore.Qt.AlignCenter)
                self.students_table.setItem(i, 3, national_item)

                # نسبة الغياب (سيتم حسابها)
                percentage_item = QtWidgets.QTableWidgetItem("0%")
                percentage_item.setTextAlignment(QtCore.Qt.AlignCenter)
                self.students_table.setItem(i, 4, percentage_item)

                # تعيين ارتفاع الصف
                self.students_table.setRowHeight(i, 50)

            # تحديث عرض الأعمدة
            self.students_table.resizeColumnsToContents()

            conn.close()

            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح',
                f'تم تحميل {len(rows)} طالب من {grade} - {section}'
            )

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في تحميل الطلاب:\n{str(e)}'
            )

    def save_attendance_internal(self):
        """حفظ الغياب داخلياً"""
        try:
            if self.students_table.rowCount() == 0:
                QtWidgets.QMessageBox.warning(
                    self, '⚠️ تحذير',
                    'لا يوجد طلاب لحفظ غيابهم. يرجى تحميل قائمة الطلاب أولاً.'
                )
                return

            from main import get_connection

            date_str = self.date_edit.date().toString('yyyy-MM-dd')

            conn = get_connection()
            cur = conn.cursor()

            # حذف السجلات السابقة لنفس التاريخ
            student_ids = []
            for i in range(self.students_table.rowCount()):
                sid = self.students_table.item(i, 1).data(QtCore.Qt.UserRole)
                student_ids.append(sid)

            if student_ids:
                placeholders = ','.join(['?'] * len(student_ids))
                cur.execute(f"""
                    DELETE FROM attendance
                    WHERE date = ? AND student_id IN ({placeholders})
                """, [date_str] + student_ids)

            # حفظ السجلات الجديدة
            absent_count = 0

            # الحصول على البيانات الإضافية
            hijri_year = self.cb_hijri_year.currentText()
            semester = self.cb_semester.currentText()
            hijri_date = self.lbl_hijri.text()

            for i in range(self.students_table.rowCount()):
                student_id = self.students_table.item(i, 1).data(QtCore.Qt.UserRole)
                student_name = self.students_table.item(i, 2).text()
                national_id = self.students_table.item(i, 2).data(QtCore.Qt.UserRole)

                checkbox = self.students_table.cellWidget(i, 0)
                if checkbox and checkbox.isChecked():
                    # طالب غائب
                    cur.execute('''
                        INSERT INTO attendance
                        (student_id, student_name, national_id, date, hijri_date,
                         hijri_year, semester, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (student_id, student_name, national_id, date_str,
                          hijri_date, hijri_year, semester, 'غائب'))
                    absent_count += 1

            conn.commit()
            conn.close()

            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح',
                f'تم حفظ الغياب بتاريخ {date_str}\n'
                f'عدد الغائبين: {absent_count}\n'
                f'عدد الحاضرين: {self.students_table.rowCount() - absent_count}'
            )

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في حفظ الغياب:\n{str(e)}'
            )

    def load_current_school_settings(self):
        """تحميل إعدادات المدرسة الحالية"""
        try:
            from main import get_connection

            conn = get_connection()
            cur = conn.cursor()

            cur.execute('SELECT * FROM school_settings WHERE id = 1')
            row = cur.fetchone()

            if row:
                self.school_name_edit.setText(row[1] or '')
                self.education_dept_edit.setText(row[2] or '')
                self.education_office_edit.setText(row[3] or '')
                self.current_hijri_year_edit.setCurrentText(row[5] or '1446')
                self.current_semester_edit.setCurrentText(row[6] or 'الفصل الأول')
                self.excel_path_edit.setText(row[7] or '')

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_school_settings(self):
        """حفظ إعدادات المدرسة"""
        try:
            from main import get_connection

            conn = get_connection()
            cur = conn.cursor()

            # تحديث الإعدادات
            cur.execute('''
                UPDATE school_settings SET
                    school_name = ?,
                    education_department = ?,
                    education_office = ?,
                    current_hijri_year = ?,
                    current_semester = ?,
                    excel_import_path = ?
                WHERE id = 1
            ''', (
                self.school_name_edit.text(),
                self.education_dept_edit.text(),
                self.education_office_edit.text(),
                self.current_hijri_year_edit.currentText(),
                self.current_semester_edit.currentText(),
                self.excel_path_edit.text()
            ))

            conn.commit()
            conn.close()

            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح',
                'تم حفظ إعدادات المدرسة بنجاح'
            )

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في حفظ الإعدادات:\n{str(e)}'
            )

    def browse_excel_file(self):
        """تصفح ملف Excel"""
        file_path, _ = QtWidgets.QFileDialog.getOpenFileName(
            self,
            'اختر ملف Excel',
            '',
            'Excel Files (*.xlsx *.xls)'
        )

        if file_path:
            self.excel_path_edit.setText(file_path)

    def import_excel_data(self):
        """استيراد البيانات من Excel"""
        try:
            excel_path = self.excel_path_edit.text()
            if not excel_path:
                QtWidgets.QMessageBox.warning(
                    self, '⚠️ تحذير',
                    'يرجى اختيار ملف Excel أولاً'
                )
                return

            import pandas as pd
            from main import get_connection

            # قراءة ملف Excel
            df = pd.read_excel(excel_path)

            # التحقق من الأعمدة المطلوبة
            required_columns = ['اسم الطالب', 'السجل المدني', 'المرحلة', 'الفصل']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                QtWidgets.QMessageBox.critical(
                    self, '❌ خطأ',
                    f'الأعمدة التالية مفقودة في ملف Excel:\n{", ".join(missing_columns)}'
                )
                return

            conn = get_connection()
            cur = conn.cursor()

            hijri_year = self.current_hijri_year_edit.currentText()
            semester = self.current_semester_edit.currentText()

            imported_count = 0
            updated_count = 0

            for _, row in df.iterrows():
                name = str(row['اسم الطالب']).strip()
                national_id = str(row['السجل المدني']).strip()
                grade = str(row['المرحلة']).strip()
                section = str(row['الفصل']).strip()

                if not all([name, national_id, grade, section]):
                    continue

                # التحقق من وجود الطالب
                cur.execute('SELECT id FROM students WHERE national_id = ?', (national_id,))
                existing = cur.fetchone()

                if existing:
                    # تحديث بيانات الطالب
                    cur.execute('''
                        UPDATE students SET
                            name = ?, grade = ?, section = ?,
                            hijri_year = ?, semester = ?,
                            updated_date = CURRENT_TIMESTAMP
                        WHERE national_id = ?
                    ''', (name, grade, section, hijri_year, semester, national_id))
                    updated_count += 1
                else:
                    # إضافة طالب جديد
                    cur.execute('''
                        INSERT INTO students
                        (name, national_id, grade, section, hijri_year, semester)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (name, national_id, grade, section, hijri_year, semester))
                    imported_count += 1

            # تحديث مسار الملف وتاريخ الاستيراد
            cur.execute('''
                UPDATE school_settings SET
                    excel_import_path = ?,
                    last_import_date = CURRENT_TIMESTAMP
                WHERE id = 1
            ''', (excel_path,))

            conn.commit()
            conn.close()

            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح',
                f'تم استيراد البيانات بنجاح:\n'
                f'طلاب جدد: {imported_count}\n'
                f'طلاب محدثين: {updated_count}\n'
                f'المجموع: {imported_count + updated_count}'
            )

        except ImportError:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ',
                'مكتبة pandas غير متوفرة\nيرجى تثبيتها باستخدام: pip install pandas openpyxl'
            )
        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في استيراد البيانات:\n{str(e)}'
            )
        
    def show_about(self):
        """عرض نافذة حول"""
        about_dialog = AboutDialog(self)
        about_dialog.exec()

    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            /* النافذة الرئيسية */
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }

            /* القائمة الجانبية */
            #sidebar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border-left: 3px solid #3498db;
            }

            /* إطار الشعار */
            #logoFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 20px;
            }

            #titleLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                margin: 5px;
            }

            #subtitleLabel {
                color: #bdc3c7;
                font-size: 12px;
                margin: 5px;
            }

            /* أزرار القائمة */
            #menuButton {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid transparent;
                border-radius: 12px;
                padding: 10px;
                text-align: right;
                margin: 2px;
            }

            #menuButton:hover {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid #3498db;
                transform: translateX(-5px);
            }

            #menuButton:pressed {
                background: rgba(255, 255, 255, 0.3);
            }

            #menuButtonTitle {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }

            #menuButtonDesc {
                color: #bdc3c7;
                font-size: 11px;
            }

            /* إطار المستخدم */
            #userFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 15px;
                margin-top: 10px;
            }

            #userLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }

            #dateLabel {
                color: #bdc3c7;
                font-size: 12px;
            }

            /* منطقة المحتوى */
            #contentArea {
                background: white;
                border-radius: 15px 0px 0px 15px;
                margin: 10px 0px 10px 0px;
            }

            /* شريط العنوان */
            #headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 20px;
            }

            #welcomeLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
            }

            /* المحتوى الرئيسي */
            #mainContent {
                background: transparent;
            }

            /* إطار الترحيب */
            #welcomeFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 40px;
                margin: 20px;
            }

            #welcomeTitle {
                color: white;
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 15px;
            }

            #welcomeDesc {
                color: rgba(255, 255, 255, 0.9);
                font-size: 16px;
                line-height: 1.6;
            }

            /* بطاقات الميزات */
            #featureCard {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid #e9ecef;
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
            }

            #featureCard:hover {
                border: 2px solid #3498db;
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
            }

            #featureTitle {
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                margin: 8px 0;
            }

            #featureDesc {
                color: #7f8c8d;
                font-size: 13px;
                line-height: 1.4;
            }
        """)


class AboutDialog(QtWidgets.QDialog):
    """نافذة حول البرنامج"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("حول البرنامج")
        self.setFixedSize(500, 600)
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QtWidgets.QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # شعار البرنامج
        logo_label = QtWidgets.QLabel("🎓")
        logo_label.setAlignment(QtCore.Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 64px; margin: 20px;")
        layout.addWidget(logo_label)

        # اسم البرنامج
        title_label = QtWidgets.QLabel("نظام إدارة الغياب المدرسي")
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        title_label.setObjectName("aboutTitle")
        layout.addWidget(title_label)

        # الإصدار
        version_label = QtWidgets.QLabel("الإصدار 2.0 - التصميم المطور")
        version_label.setAlignment(QtCore.Qt.AlignCenter)
        version_label.setObjectName("aboutVersion")
        layout.addWidget(version_label)

        # خط فاصل
        line = QtWidgets.QFrame()
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(line)

        # معلومات المطور
        dev_frame = QtWidgets.QFrame()
        dev_frame.setObjectName("devFrame")
        dev_layout = QtWidgets.QVBoxLayout(dev_frame)

        dev_title = QtWidgets.QLabel("👨‍💻 معلومات المطور")
        dev_title.setObjectName("sectionTitle")
        dev_layout.addWidget(dev_title)

        dev_info = QtWidgets.QLabel("""
        <b>المطور:</b> Ahmed Rabie<br>
        <b>الواتساب:</b> 00201154013932<br>
        <b>البريد الإلكتروني:</b> <EMAIL><br>
        <b>التخصص:</b> تطوير أنظمة إدارة المدارس
        """)
        dev_info.setObjectName("infoText")
        dev_layout.addWidget(dev_info)

        layout.addWidget(dev_frame)

        # ميزات البرنامج
        features_frame = QtWidgets.QFrame()
        features_frame.setObjectName("featuresFrame")
        features_layout = QtWidgets.QVBoxLayout(features_frame)

        features_title = QtWidgets.QLabel("✨ ميزات البرنامج")
        features_title.setObjectName("sectionTitle")
        features_layout.addWidget(features_title)

        features_list = QtWidgets.QLabel("""
        • إدارة شاملة لغياب الطلاب
        • تقارير متقدمة مع إحصائيات دقيقة
        • حفظ بصيغ متعددة (PDF, Word, Excel)
        • طباعة احترافية مع معاينة
        • واجهة مستخدم عصرية وسهلة
        • دعم التاريخ الهجري والميلادي
        • إعدادات مرنة للمدرسة
        """)
        features_list.setObjectName("infoText")
        features_layout.addWidget(features_list)

        layout.addWidget(features_frame)

        # أزرار التواصل
        contact_layout = QtWidgets.QHBoxLayout()

        whatsapp_btn = QtWidgets.QPushButton("📱 واتساب")
        whatsapp_btn.setObjectName("contactBtn")
        whatsapp_btn.clicked.connect(self.open_whatsapp)

        email_btn = QtWidgets.QPushButton("📧 إيميل")
        email_btn.setObjectName("contactBtn")
        email_btn.clicked.connect(self.open_email)

        contact_layout.addWidget(whatsapp_btn)
        contact_layout.addWidget(email_btn)

        layout.addLayout(contact_layout)

        # زر إغلاق
        close_btn = QtWidgets.QPushButton("إغلاق")
        close_btn.setObjectName("closeBtn")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

    def open_whatsapp(self):
        """فتح واتساب"""
        import webbrowser
        webbrowser.open("https://wa.me/00201154013932")

    def open_email(self):
        """فتح البريد الإلكتروني"""
        import webbrowser
        webbrowser.open("mailto:<EMAIL>")

    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }

            #aboutTitle {
                color: white;
                font-size: 24px;
                font-weight: bold;
                margin: 10px;
            }

            #aboutVersion {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                margin: 5px;
            }

            #devFrame, #featuresFrame {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 10px;
                padding: 20px;
                margin: 10px 0;
            }

            #sectionTitle {
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }

            #infoText {
                color: #34495e;
                font-size: 13px;
                line-height: 1.6;
            }

            #contactBtn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }

            #contactBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }

            #closeBtn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 30px;
                font-weight: bold;
                font-size: 14px;
                margin-top: 10px;
            }

            #closeBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ec7063, stop:1 #e74c3c);
            }
        """)


if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)

    # تطبيق خط عربي
    font = QtGui.QFont("Segoe UI", 10)
    app.setFont(font)

    window = ModernMainWindow()
    window.show()

    sys.exit(app.exec())
