🎓 دليل الاستخدام السريع - نظام إدارة الغياب المدرسي المحسن
================================================================

📋 المحتويات:
1. التثبيت والتشغيل
2. استيراد بيانات الطلاب
3. تسجيل الغياب اليومي
4. عرض التقارير
5. الطباعة والحفظ
6. النسخ الاحتياطية
7. حل المشاكل الشائعة

================================================================

1️⃣ التثبيت والتشغيل
=====================

🔧 التثبيت:
• انقر مرتين على "تثبيت_النظام.bat"
• أو اكتب في سطر الأوامر: python install.py

🚀 التشغيل:
• انقر مرتين على "تشغيل_النظام.bat"
• أو اكتب في سطر الأوامر: python run_app.py

================================================================

2️⃣ استيراد بيانات الطلاب
========================

📊 خطوات الاستيراد:
1. حضر ملف Excel يحتوي على:
   - العمود C: الفصل
   - العمود D: رمز المرحلة (0725, 0825, 0925)
   - العمود E: اسم الطالب
   - العمود F: الرقم الوطني

2. انقر على "📊 استيراد ملف Excel"
3. اختر الملف من جهازك
4. انتظر رسالة التأكيد

💡 ملاحظة: يجب أن تكون البيانات في الورقة "Sheet2"

================================================================

3️⃣ تسجيل الغياب اليومي
======================

📝 خطوات التسجيل:
1. اختر المرحلة من القائمة المنسدلة
2. اختر الفصل من القائمة المنسدلة
3. انقر على "👥 تحميل قائمة الطلاب"
4. حدد التاريخ (سيظهر التاريخ الهجري تلقائياً)
5. ضع علامة ✓ أمام الطلاب الغائبين
6. انقر على "💾 حفظ الغياب"

🔍 ملاحظات:
• ستظهر إحصائيات كل طالب في العمود الأخير
• الألوان تدل على مستوى الغياب (أخضر: قليل، أصفر: متوسط، أحمر: كثير)

================================================================

4️⃣ عرض التقارير
===============

📊 أنواع التقارير:

🧑‍🎓 تقرير طالب:
• انقر على "👤 تقرير طالب"
• اختر الطالب من القائمة
• سيظهر تاريخ كل غياب مع التاريخ الهجري

🏫 تقرير فصل:
• انقر على "🏫 تقرير فصل"
• سيظهر إحصائيات جميع طلاب الفصل المحدد

🏢 تقرير مدرسة:
• انقر على "🏢 تقرير مدرسة"
• سيظهر إحصائيات جميع طلاب المدرسة

📈 تقارير متقدمة:
• انقر على "📊 تقارير متقدمة"
• اختر نوع التقرير (مدرسة/فصل/صف)
• سيظهر تقارير أسبوعية وشهرية وسنوية

📊 الإحصائيات:
• انقر على "📈 الإحصائيات"
• سيظهر لوحة إحصائيات مرئية شاملة

================================================================

5️⃣ الطباعة والحفظ
=================

💾 خيارات الحفظ:
• 📄 حفظ PDF: ملف PDF منسق ومهيأ للطباعة
• 📝 حفظ Word: مستند Word قابل للتعديل
• 📊 حفظ Excel: جدول بيانات مع تنسيق متقدم

🖨️ الطباعة:
• انقر على "🖨️ طباعة"
• حدد إعدادات الطباعة:
  - اتجاه الصفحة (عمودي/أفقي)
  - التكبير/التصغير
  - الهوامش
  - تضمين الرأس والتذييل
• انقر على "👁️ معاينة" لرؤية النتيجة

================================================================

6️⃣ النسخ الاحتياطية
==================

💾 إنشاء نسخة احتياطية:
• انقر على "💾 نسخ احتياطي"
• اختر مجلد الحفظ
• سيتم حفظ نسخة من قاعدة البيانات

🔄 استعادة النسخة الاحتياطية:
• انسخ ملف النسخة الاحتياطية إلى مجلد النظام
• أعد تسميته إلى "attendance.db.enc"
• أعد تشغيل النظام

================================================================

7️⃣ حل المشاكل الشائعة
=====================

❌ مشكلة: "ModuleNotFoundError"
الحل: تثبيت المكتبة المفقودة
pip install [اسم المكتبة]

❌ مشكلة: خطأ في قاعدة البيانات
الحل: حذف ملفات قاعدة البيانات وإعادة إنشائها
احذف: attendance.db و attendance.db.enc
أعد تشغيل النظام

❌ مشكلة: لا تظهر البيانات
الحل: تأكد من:
• استيراد ملف Excel صحيح
• اختيار المرحلة والفصل الصحيحين
• تحميل قائمة الطلاب

❌ مشكلة: خطأ في الطباعة
الحل: تأكد من:
• تثبيت طابعة على النظام
• صحة إعدادات الطباعة
• وجود مساحة كافية على القرص

❌ مشكلة: بطء في النظام
الحل:
• أغلق البرامج الأخرى
• أعد تشغيل النظام
• تأكد من وجود مساحة كافية على القرص

================================================================

📞 للمساعدة الإضافية:
===================

📖 راجع ملف README.md للتفاصيل الكاملة
🔧 استخدم ملف install.py للتثبيت التلقائي
🚀 استخدم ملف run_app.py للتشغيل مع الفحص التلقائي

================================================================

💡 نصائح مهمة:
==============

✅ احتفظ بنسخة احتياطية دورية
✅ لا تحذف ملف config.py
✅ استخدم ملفات Excel منظمة
✅ سجل الغياب يومياً لضمان دقة التقارير
✅ راجع الإحصائيات بانتظام لمتابعة الأداء

================================================================

🎓 شكراً لاستخدام نظام إدارة الغياب المدرسي المحسن!
================================================================
