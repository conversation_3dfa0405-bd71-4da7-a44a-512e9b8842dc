#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة الغياب المدرسي - التصميم الحديث
"""

import os
import sys
from PySide6 import QtWidgets, QtCore, QtG<PERSON>

def main():
    """الدالة الرئيسية لتشغيل النظام"""

    # تقليل التحذيرات
    os.environ['QT_MAC_WANTS_LAYER'] = '1'
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

    print("🎓 نظام إدارة الغياب المدرسي - التصميم الحديث")
    print("=" * 60)

    # إنشاء التطبيق
    app = QtWidgets.QApplication(sys.argv)

    # تعيين خصائص التطبيق
    app.setApplicationName("نظام إدارة الغياب المدرسي")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Ahmed Rabie")

    # تطبيق خط عربي مناسب
    try:
        font = QtGui.QFont("Arial", 10)
        app.setFont(font)
    except:
        pass

    # تعيين اتجاه النص للعربية
    app.setLayoutDirection(QtCore.Qt.RightToLeft)

    # عرض خيارات التشغيل
    print("اختر طريقة التشغيل:")
    print("1️⃣  التصميم الحديث (مستحسن)")
    print("2️⃣  التصميم التقليدي")
    print("3️⃣  تشغيل تلقائي للتصميم الحديث")
    print("-" * 60)

    try:
        # محاولة تشغيل التصميم الحديث أولاً
        print("🚀 تشغيل التصميم الحديث...")
        from modern_ui import ModernMainWindow

        # إنشاء النافذة الحديثة
        window = ModernMainWindow()
        window.show()

        print("✅ تم تشغيل التصميم الحديث بنجاح!")
        print("📱 النافذة مفتوحة الآن - يمكنك استخدام النظام")
        print("🔄 لإغلاق النظام، أغلق النافذة أو اضغط Ctrl+C هنا")
        print("=" * 60)

        # تشغيل التطبيق
        sys.exit(app.exec())

    except ImportError as e:
        print(f"⚠️  فشل في تحميل التصميم الحديث: {str(e)}")
        print("🔄 التبديل إلى التصميم التقليدي...")

        # تشغيل النسخة التقليدية
        try:
            from main import MainWindow
            app_window = MainWindow()
            app_window.show()

            print("✅ تم تشغيل التصميم التقليدي بنجاح!")
            print("📱 النافذة مفتوحة الآن - يمكنك استخدام النظام")
            print("=" * 60)

            sys.exit(app.exec())

        except Exception as e2:
            print(f"❌ فشل في تشغيل النظام: {str(e2)}")
            input("اضغط Enter للخروج...")
            sys.exit(1)

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()
