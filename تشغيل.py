#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل بسيط - نظام إدارة الغياب المدرسي
"""

import os
import sys

# تقليل التحذيرات
os.environ['QT_MAC_WANTS_LAYER'] = '1'
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

print("🎓 نظام إدارة الغياب المدرسي المحسن")
print("🚀 تشغيل النظام...")

try:
    # تشغيل النظام الرئيسي
    exec(open('main.py').read())
except FileNotFoundError:
    print("❌ ملف main.py غير موجود")
except Exception as e:
    print(f"❌ خطأ: {e}")
    input("اضغط Enter للخروج...")
