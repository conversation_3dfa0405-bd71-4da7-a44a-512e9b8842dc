#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل بسيط لنظام إدارة الغياب
Simple launcher for School Attendance Management System
"""

import sys
import os

def main():
    print("🎓 نظام إدارة الغياب المدرسي")
    print("=" * 40)
    
    # تعيين متغيرات البيئة لتقليل التحذيرات في macOS
    os.environ['QT_MAC_WANTS_LAYER'] = '1'
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    try:
        print("🚀 تشغيل النظام...")

        # استيراد وتشغيل النظام الرئيسي
        import main

        # تشغيل التطبيق
        app = main.QtWidgets.QApplication(main.sys.argv)

        # تطبيق خط عربي جميل
        font = main.QtGui.QFont("Arial", 10)
        app.setFont(font)

        win = main.MainWindow()
        win.show()

        print("✅ تم تشغيل النظام بنجاح!")
        print("📝 يمكنك الآن استخدام النظام من النافذة المفتوحة")

        # تشغيل حلقة الأحداث
        code = app.exec()

        # تنظيف عند الإغلاق
        main.encrypt_and_cleanup()
        main.sys.exit(code)

    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 جرب تثبيت المتطلبات:")
        print("pip3 install PySide6 pandas openpyxl cryptography")
        input("اضغط Enter للخروج...")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
