#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل بسيط لنظام إدارة الغياب
Simple launcher for School Attendance Management System
"""

import sys
import os

def main():
    print("🎓 نظام إدارة الغياب المدرسي")
    print("=" * 40)
    
    # تعيين متغيرات البيئة لتقليل التحذيرات في macOS
    os.environ['QT_MAC_WANTS_LAYER'] = '1'
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    try:
        print("🚀 تشغيل النظام...")
        import main
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 جرب تثبيت المتطلبات:")
        print("pip3 install PySide6 pandas openpyxl cryptography")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    main()
