🎓 دليل تشغيل نظام إدارة الغياب المدرسي المحسن
================================================

📋 ملخص المشكلة:
- الأمر python3 main.py يشغل النسخة القديمة
- الأمر python3 start.py لا يعمل بشكل صحيح

✅ الحل - طرق التشغيل الصحيحة:

================================================
🚀 الطريقة الأولى (الأفضل):
================================================
python3 run.py

📝 هذا الملف:
- يفحص جميع المتطلبات تلقائياً
- يعرض رسائل واضحة
- يشغل النسخة المحسنة
- يقلل التحذيرات في macOS

================================================
🚀 الطريقة الثانية (بسيطة):
================================================
python3 تشغيل.py

📝 هذا الملف:
- تشغيل مباشر وبسيط
- بدون فحص المتطلبات
- يشغل النسخة المحسنة

================================================
🚀 الطريقة الثالثة (للويندوز):
================================================
انقر مرتين على: تشغيل_النظام.bat

================================================
⚠️ تجنب هذه الطرق:
================================================
❌ python3 main.py    (يشغل النسخة القديمة)
❌ python3 start.py   (لا يعمل بشكل صحيح)

================================================
🔧 إذا لم تعمل أي طريقة:
================================================

1. تأكد من تثبيت المتطلبات:
   pip3 install PySide6 pandas openpyxl cryptography

2. تأكد من وجود الملفات:
   - main.py
   - config.py
   - run.py

3. جرب الأمر:
   python run.py
   (بدون 3)

================================================
📱 علامات نجاح التشغيل:
================================================
✅ ظهور رسالة "تم تشغيل النظام بنجاح!"
✅ فتح نافذة النظام
✅ ظهور الواجهة المحسنة بالألوان الجديدة

================================================
🎨 مميزات النسخة المحسنة:
================================================
✅ تصميم عصري بألوان متدرجة
✅ خط أكبر وأوضح للأسماء
✅ تواريخ هجرية بالأرقام
✅ تواريخ ميلادية بتنسيق جميل
✅ جدول أكبر ومريح أكثر
✅ طباعة وحفظ محسن

================================================
💡 نصائح مهمة:
================================================
• استخدم python3 run.py للتشغيل الآمن
• تجاهل تحذيرات macOS (طبيعية)
• احفظ نسخ احتياطية بانتظام
• استخدم الإحصائيات لمتابعة الأداء

================================================
📞 للمساعدة:
================================================
إذا واجهت مشاكل:
1. تأكد من تثبيت Python 3.8+
2. تأكد من تثبيت المكتبات المطلوبة
3. تأكد من وجود جميع الملفات
4. جرب python run.py (بدون 3)

================================================
