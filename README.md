# 🎓 نظام إدارة الغياب المدرسي المحسن

## Enhanced School Attendance Management System

نظام شامل ومحسن لإدارة غياب الطلاب في المدارس بتصميم عصري وواجهة سهلة الاستخدام.

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![PySide6](https://img.shields.io/badge/PySide6-6.5+-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## ✨ المميزات الجديدة

### 🎨 تصميم عصري وجذاب

- واجهة مستخدم محسنة بألوان متدرجة
- أيقونات تعبيرية لسهولة الاستخدام
- تخطيط منظم ومقسم إلى أقسام واضحة
- دعم كامل للغة العربية

### 📊 إحصائيات مرئية متقدمة

- لوحة إحصائيات تفاعلية
- رسوم بيانية للغياب
- تقارير مفصلة بالألوان
- تتبع أداء الطلاب

### 🖨️ نظام طباعة محسن

- معاينة الطباعة المتقدمة
- إعدادات طباعة مخصصة
- تحكم في الهوامش والتكبير
- رؤوس وتذييلات احترافية

### 💾 حفظ متعدد الصيغ

- تصدير PDF محسن مع تنسيق احترافي
- حفظ Word مع جداول منسقة
- تصدير Excel مع تنسيق متقدم
- نسخ احتياطية تلقائية

### 🔒 أمان محسن

- تشفير قاعدة البيانات
- نسخ احتياطية آمنة
- حماية البيانات الحساسة

## 📋 المتطلبات

### متطلبات النظام

- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

### المكتبات المطلوبة

```
PySide6>=6.5.0          # واجهة المستخدم الرسومية
pandas>=1.5.0           # معالجة البيانات
openpyxl>=3.0.0         # ملفات Excel
xlrd>=2.0.0             # قراءة ملفات Excel القديمة
cryptography>=3.4.0     # التشفير
hijri-converter>=2.2.0  # التواريخ الهجرية
python-docx>=0.8.11     # ملفات Word
matplotlib>=3.5.0       # الرسوم البيانية
seaborn>=0.11.0         # إحصائيات مرئية
Pillow>=8.0.0           # معالجة الصور
```

## 🚀 التثبيت والتشغيل

### الطريقة السريعة (مستحسنة)

```bash
# الطريقة الأفضل - مع فحص المتطلبات
python3 run.py

# أو الطريقة البسيطة
python3 تشغيل.py

# ⚠️ تجنب هذه الطرق (تشغل النسخة القديمة):
# python3 main.py
# python3 start.py
```

### للويندوز

```batch
# تثبيت سريع
تثبيت_النظام.bat

# تشغيل سريع
تشغيل_النظام.bat
```

### التثبيت اليدوي

```bash
# 1. تثبيت المتطلبات الأساسية
pip3 install PySide6 pandas openpyxl cryptography

# 2. تثبيت المتطلبات الاختيارية
pip3 install hijri-converter python-docx

# 3. تشغيل النظام
python3 main.py
```

### إنشاء بيئة افتراضية (اختياري)

```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل النظام
python main.py
```

## 📖 دليل الاستخدام

### 1. استيراد بيانات الطلاب

- انقر على "📊 استيراد ملف Excel"
- اختر ملف Excel يحتوي على بيانات الطلاب
- تأكد من أن الملف يحتوي على الأعمدة: الاسم، المرحلة، الفصل

### 2. تسجيل الغياب

- اختر المرحلة والفصل
- انقر على "👥 تحميل قائمة الطلاب"
- حدد التاريخ (ميلادي/هجري)
- ضع علامة ✓ أمام الطلاب الغائبين
- انقر على "💾 حفظ الغياب"

### 3. عرض التقارير

- **تقرير طالب**: غياب طالب محدد
- **تقرير فصل**: إحصائيات الفصل
- **تقرير مدرسة**: إحصائيات عامة
- **تقارير متقدمة**: تقارير مفصلة بفترات زمنية
- **الإحصائيات**: لوحة إحصائيات مرئية

### 4. الطباعة والحفظ

- انقر على أي زر حفظ/طباعة في التقارير
- اختر الصيغة المطلوبة (PDF, Word, Excel)
- حدد إعدادات الطباعة المناسبة
- احفظ أو اطبع التقرير

## 🔧 الإعدادات

### ملف config.py

```python
DB_FILE_PLAIN = 'attendance.db'           # ملف قاعدة البيانات
DB_FILE_ENCRYPTED = 'attendance.db.enc'   # ملف قاعدة البيانات المشفر
FILE_KEY = b'your-encryption-key-here'     # مفتاح التشفير
```

## 📁 هيكل المشروع

```
absences-system2/
├── main.py              # الملف الرئيسي للنظام
├── config.py            # ملف الإعدادات
├── run_app.py           # ملف التشغيل المحسن
├── requirements.txt     # المتطلبات
├── README.md           # دليل الاستخدام
├── attendance.db.enc   # قاعدة البيانات المشفرة
└── venv/              # البيئة الافتراضية (اختياري)
```

## 🆕 التحديثات في الإصدار 2.0

### تحسينات الواجهة

- ✅ تصميم عصري بألوان متدرجة
- ✅ أيقونات تعبيرية
- ✅ تخطيط محسن ومنظم
- ✅ دعم أفضل للعربية

### تحسينات الوظائف

- ✅ إحصائيات مرئية متقدمة
- ✅ نظام طباعة محسن
- ✅ حفظ متعدد الصيغ
- ✅ نسخ احتياطية
- ✅ معالجة أفضل للأخطاء

### تحسينات الأداء

- ✅ تحميل أسرع للبيانات
- ✅ ذاكرة محسنة
- ✅ استجابة أفضل للواجهة

## 🐛 حل المشاكل الشائعة

### مشكلة: "ModuleNotFoundError"

```bash
# الحل: تثبيت المكتبة المفقودة
pip3 install [اسم المكتبة]

# أو تثبيت المتطلبات الأساسية
pip3 install PySide6 pandas openpyxl cryptography
```

### مشكلة: البرنامج لا يعمل مع python3

```bash
# جرب هذه البدائل:
python3 start.py    # الأفضل
python start.py
python3 main.py
python main.py

# للويندوز:
python start.py
تشغيل_النظام.bat
```

### مشكلة: تحذيرات في macOS

```bash
# هذه التحذيرات طبيعية ولا تؤثر على عمل البرنامج:
# TSMSendMessageToUIServer: CFMessagePortSendRequest FAILED

# للتقليل من التحذيرات استخدم:
python3 start.py
```

### مشكلة: خطأ في قاعدة البيانات

```bash
# الحل: حذف ملف قاعدة البيانات وإعادة إنشائها
rm attendance.db*
python3 main.py
```

### مشكلة: خطأ في التشفير

- تأكد من صحة مفتاح التشفير في config.py
- أو قم بإنشاء مفتاح جديد

### مشكلة: التواريخ الهجرية لا تظهر

```bash
# تثبيت مكتبة التواريخ الهجرية
pip3 install hijri-converter
```

### مشكلة: لا يمكن حفظ ملفات Word

```bash
# تثبيت مكتبة Word
pip3 install python-docx
```

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك اقتراحات:

1. تأكد من تثبيت جميع المتطلبات
2. راجع دليل الاستخدام
3. تحقق من ملف config.py

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

شكراً لاستخدام نظام إدارة الغياب المدرسي المحسن!
