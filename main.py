import os
import sys
import sqlite3
import datetime
import pandas as pd
import xlrd  # لدعم قراءة ملفات .xls
from cryptography.fernet import Fernet
from PySide6 import QtWidgets, QtCore, QtGui
from PySide6.QtPrintSupport import QPrinter, QPrintPreviewDialog
try:
    from hijri_converter import Gregorian  # لتحويل التاريخ إلى هجري
    HIJRI_AVAILABLE = True
except ImportError:
    HIJRI_AVAILABLE = False
    print("تحذير: مكتبة hijri-converter غير متوفرة. سيتم تعطيل التواريخ الهجرية.")

try:
    from docx import Document  # لحفظ تقارير Word
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("تحذير: مكتبة python-docx غير متوفرة. سيتم تعطيل حفظ ملفات Word.")
import config

# خريطة الأشهر الهجرية بالعربية
hijri_months = {
    1: 'محرم', 2: 'صفر', 3: 'ربيع الأول', 4: 'ربيع الآخر',
    5: 'جمادى الأولى', 6: 'جمادى الآخرة', 7: 'رجب',
    8: 'شعبان', 9: 'رمضان', 10: 'شوال', 11: 'ذو القعدة', 12: 'ذو الحجة'
}

# --- تشفير وفك التشفير ---

def ensure_plain_db():
    if not os.path.exists(config.DB_FILE_ENCRYPTED):
        open(config.DB_FILE_PLAIN, 'wb').close()
    else:
        with open(config.DB_FILE_ENCRYPTED, 'rb') as f:
            data = f.read()
        plain = Fernet(config.FILE_KEY).decrypt(data)
        with open(config.DB_FILE_PLAIN, 'wb') as f:
            f.write(plain)


def encrypt_and_cleanup():
    if os.path.exists(config.DB_FILE_PLAIN):
        with open(config.DB_FILE_PLAIN, 'rb') as f:
            data = f.read()
        enc = Fernet(config.FILE_KEY).encrypt(data)
        with open(config.DB_FILE_ENCRYPTED, 'wb') as f:
            f.write(enc)
        os.remove(config.DB_FILE_PLAIN)

# --- اتصال بقاعدة البيانات ---

def get_connection():
    return sqlite3.connect(config.DB_FILE_PLAIN)


def init_db():
    conn = get_connection()
    cur = conn.cursor()

    # إنشاء جدول الطلاب
    cur.execute("""
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            grade TEXT NOT NULL,
            section TEXT NOT NULL,
            national_id TEXT,
            hijri_year TEXT DEFAULT '1446',
            semester TEXT DEFAULT 'الفصل الأول'
        );
    """)

    # إنشاء جدول الغياب
    cur.execute("""
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            date TEXT NOT NULL,
            hijri_date TEXT,
            status TEXT CHECK(status IN ('present','absent')) NOT NULL,
            hijri_year TEXT DEFAULT '1446',
            semester TEXT DEFAULT 'الفصل الأول',
            FOREIGN KEY (student_id) REFERENCES students (id)
        );
    """)

    # إنشاء جدول إعدادات المدرسة
    cur.execute("""
        CREATE TABLE IF NOT EXISTS school_settings (
            id INTEGER PRIMARY KEY,
            school_name TEXT,
            education_department TEXT,
            education_office TEXT,
            current_hijri_year TEXT DEFAULT '1446',
            current_semester TEXT DEFAULT 'الفصل الأول',
            excel_import_path TEXT,
            logo_path TEXT
        );
    """)

    # إدراج إعدادات افتراضية إذا لم تكن موجودة
    cur.execute("SELECT COUNT(*) FROM school_settings")
    if cur.fetchone()[0] == 0:
        cur.execute("""
            INSERT INTO school_settings
            (id, school_name, education_department, education_office, current_hijri_year, current_semester)
            VALUES (1, 'مدرسة الأمل الابتدائية', 'الإدارة العامة للتعليم بمنطقة الرياض',
                    'مكتب التعليم بشرق الرياض', '1446', 'الفصل الأول')
        """)

    conn.commit()
    conn.close()

# --- إعداد نطاقات التقارير ---

def period_range(period):
    today = datetime.date.today()
    if period == 'weekly':
        start = today - datetime.timedelta(days=today.weekday())
        end = start + datetime.timedelta(days=6)
    elif period == 'monthly':
        start = today.replace(day=1)
        next_month = start.month % 12 + 1
        last_day = (start.replace(month=next_month, day=1) - datetime.timedelta(days=1)).day
        end = start.replace(day=last_day)
    else:  # yearly
        start = today.replace(month=1, day=1)
        end = today.replace(month=12, day=31)
    return start.isoformat(), end.isoformat()

# --- استخراج تقرير ---

def get_report(start_date, end_date, where_clause='', params=()):
    conn = get_connection()
    cur = conn.cursor()
    query = f"""
        SELECT s.name,
               SUM(CASE WHEN a.status='absent' THEN 1 ELSE 0 END) AS absent_count,
               COUNT(a.id) AS total_count
        FROM students s
        LEFT JOIN attendance a ON s.id = a.student_id AND a.date BETWEEN ? AND ? {where_clause}
        GROUP BY s.id;
    """
    cur.execute(query, (start_date, end_date, *params))
    rows = cur.fetchall()
    conn.close()
    report = []
    for name, absent, total in rows:
        pct = round(absent / total * 100, 2) if total else 0
        report.append((name, absent, total, pct))
    return report

class MainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        # تصميم عصري وأنيق مع ألوان متدرجة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            }

            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a90e2, stop:1 #357abd);
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5ba0f2, stop:1 #4a90e2);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #357abd, stop:1 #2968a3);
            }

            QLabel {
                font-size: 14px;
                color: #2c3e50;
                font-weight: 500;
            }

            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #dee2e6;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                selection-background-color: #e3f2fd;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6c757d, stop:1 #495057);
                color: white;
                padding: 10px;
                font-weight: bold;
                font-size: 14px;
                border: none;
                border-right: 1px solid #495057;
            }

            QDateEdit, QComboBox {
                padding: 8px 12px;
                font-size: 14px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                min-height: 20px;
            }
            QDateEdit:focus, QComboBox:focus {
                border-color: #4a90e2;
                outline: none;
            }

            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a90e2, stop:1 #357abd);
                color: white;
            }
        """)
        self.setWindowTitle('🎓 نظام إدارة الغياب المدرسي')
        self.setWindowIcon(QtGui.QIcon('📚'))
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.resize(1200, 800)
        ensure_plain_db()
        init_db()
        self.setup_ui()
        self.populate_grade_section()
        self.update_hijri()

    def setup_menubar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu('📁 ملف')

        # إعدادات المدرسة
        school_settings_action = QtGui.QAction('⚙️ إعدادات المدرسة', self)
        school_settings_action.triggered.connect(self.show_school_settings)
        file_menu.addAction(school_settings_action)

        file_menu.addSeparator()

        # خروج
        exit_action = QtGui.QAction('🚪 خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu('❓ مساعدة')

        # حول البرنامج
        about_action = QtGui.QAction('ℹ️ حول', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_ui(self):
        # إعداد شريط القوائم
        self.setup_menubar()

        central = QtWidgets.QWidget()
        self.setCentralWidget(central)
        main_layout = QtWidgets.QVBoxLayout(central)
        main_layout.setSpacing(15)  # تقليل المسافات بين الأقسام
        main_layout.setContentsMargins(15, 15, 15, 15)  # تقليل الهوامش

        # شريط العنوان مع الأيقونات
        title_frame = QtWidgets.QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 12px;
                padding: 15px;
            }
        """)
        title_layout = QtWidgets.QHBoxLayout(title_frame)

        title_label = QtWidgets.QLabel('🎓 نظام إدارة الغياب المدرسي')
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # زر الإعدادات
        settings_btn = QtWidgets.QPushButton('⚙️ إعدادات المدرسة')
        settings_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
            }
        """)
        settings_btn.clicked.connect(self.show_school_settings)
        title_layout.addWidget(settings_btn)
        main_layout.addWidget(title_frame)

        # قسم الاستيراد والإعداد - مضغوط
        import_frame = self.create_compact_section_frame('📁 استيراد البيانات')
        import_layout = QtWidgets.QHBoxLayout()

        btn_imp = QtWidgets.QPushButton('📊 استيراد ملف Excel')
        btn_imp.clicked.connect(self.import_excel)
        btn_imp.setMinimumHeight(35)  # تقليل الارتفاع
        import_layout.addWidget(btn_imp)

        btn_backup = QtWidgets.QPushButton('💾 نسخ احتياطي')
        btn_backup.clicked.connect(self.create_backup)
        btn_backup.setMinimumHeight(35)  # تقليل الارتفاع
        import_layout.addWidget(btn_backup)

        import_layout.addStretch()
        import_frame.layout().addLayout(import_layout)
        main_layout.addWidget(import_frame)

        # قسم اختيار الفصل - مضغوط
        class_frame = self.create_compact_section_frame('🏫 اختيار الفصل الدراسي')
        class_layout = QtWidgets.QGridLayout()

        class_layout.addWidget(QtWidgets.QLabel('📚 المرحلة:'), 0, 0)
        self.cb_grade = QtWidgets.QComboBox()
        self.cb_grade.setMinimumHeight(30)  # تقليل الارتفاع
        class_layout.addWidget(self.cb_grade, 0, 1)

        class_layout.addWidget(QtWidgets.QLabel('🎯 الفصل:'), 0, 2)
        self.cb_section = QtWidgets.QComboBox()
        self.cb_section.setMinimumHeight(30)  # تقليل الارتفاع
        class_layout.addWidget(self.cb_section, 0, 3)

        btn_load = QtWidgets.QPushButton('👥 تحميل قائمة الطلاب')
        btn_load.clicked.connect(self.load_students)
        btn_load.setMinimumHeight(30)  # تقليل الارتفاع
        class_layout.addWidget(btn_load, 0, 4)

        class_frame.layout().addLayout(class_layout)
        main_layout.addWidget(class_frame)

        # قسم التاريخ - مضغوط
        date_frame = self.create_compact_section_frame('📅 تحديد التاريخ')
        date_layout = QtWidgets.QHBoxLayout()

        date_layout.addWidget(QtWidgets.QLabel('📆 التاريخ:'))
        self.date_edit = QtWidgets.QDateEdit(QtCore.QDate.currentDate())
        self.date_edit.setDisplayFormat('yyyy/MM/dd')
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setMinimumHeight(30)  # تقليل الارتفاع
        self.date_edit.setStyleSheet("""
            QDateEdit {
                font-size: 15px;
                font-weight: bold;
                padding: 6px 10px;
                border: 2px solid #4a90e2;
                border-radius: 6px;
                background-color: white;
            }
        """)
        self.date_edit.dateChanged.connect(self.update_hijri)
        self.date_edit.dateChanged.connect(self.reload_students_if_loaded)
        date_layout.addWidget(self.date_edit)

        date_layout.addWidget(QtWidgets.QLabel('🌙 التاريخ الهجري:'))
        self.lbl_hijri = QtWidgets.QLabel()
        self.lbl_hijri.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                padding: 6px 10px;
                border-radius: 6px;
                border: 2px solid #28a745;
                min-height: 18px;
                font-size: 15px;
                font-weight: bold;
                color: #155724;
            }
        """)
        date_layout.addWidget(self.lbl_hijri)
        date_layout.addStretch()

        date_frame.layout().addLayout(date_layout)
        main_layout.addWidget(date_frame)

        # قسم جدول الغياب مع مساحة أكبر
        table_frame = self.create_section_frame('📋 تسجيل الغياب')
        self.table = QtWidgets.QTableWidget(0, 4)
        self.table.setHorizontalHeaderLabels(['#', '👤 اسم الطالب', '❌ غائب', '📊 الإحصائيات'])
        self.table.horizontalHeader().setSectionResizeMode(1, QtWidgets.QHeaderView.Stretch)
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.table.setMinimumHeight(500)  # زيادة الارتفاع أكثر

        # تحسين تنسيق الجدول
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #dee2e6;
                border: 2px solid #4a90e2;
                border-radius: 10px;
                selection-background-color: #e3f2fd;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                font-size: 15px;
                font-weight: 500;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a90e2, stop:1 #357abd);
                color: white;
                padding: 12px;
                font-weight: bold;
                font-size: 15px;
                border: none;
                border-right: 1px solid #357abd;
            }
        """)

        table_frame.layout().addWidget(self.table)
        main_layout.addWidget(table_frame)

        # قسم الأزرار الرئيسية
        buttons_frame = self.create_section_frame('🔧 العمليات الرئيسية')
        buttons_layout = QtWidgets.QGridLayout()

        button_configs = [
            ('💾 حفظ الغياب', self.save_attendance, '#28a745', 0, 0),
            ('👤 تقرير طالب', self.show_student_report, '#17a2b8', 0, 1),
            ('🏫 تقرير فصل', self.show_class_report, '#ffc107', 0, 2),
            ('🏢 تقرير مدرسة', self.show_school_report, '#dc3545', 1, 0),
            ('📊 تقارير متقدمة', self.show_reports, '#6f42c1', 1, 1),
            ('📈 الإحصائيات', self.show_statistics, '#fd7e14', 1, 2)
        ]

        for text, func, color, row, col in button_configs:
            btn = QtWidgets.QPushButton(text)
            btn.clicked.connect(func)
            btn.setMinimumHeight(50)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:1 {self.darken_color(color)});
                    font-size: 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {self.lighten_color(color)}, stop:1 {color});
                }}
            """)
            buttons_layout.addWidget(btn, row, col)

        buttons_frame.layout().addLayout(buttons_layout)
        main_layout.addWidget(buttons_frame)

    def create_section_frame(self, title):
        """إنشاء إطار قسم مع عنوان"""
        frame = QtWidgets.QGroupBox(title)
        frame.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        layout = QtWidgets.QVBoxLayout(frame)
        layout.setSpacing(10)
        return frame

    def create_compact_section_frame(self, title):
        """إنشاء إطار قسم مضغوط مع عنوان"""
        frame = QtWidgets.QGroupBox(title)
        frame.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 5px;
                padding-top: 5px;
                background-color: white;
                max-height: 80px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        layout = QtWidgets.QVBoxLayout(frame)
        layout.setSpacing(5)
        layout.setContentsMargins(10, 5, 10, 5)
        return frame

    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            '#28a745': '#1e7e34',
            '#17a2b8': '#117a8b',
            '#ffc107': '#e0a800',
            '#dc3545': '#c82333',
            '#6f42c1': '#59359a',
            '#fd7e14': '#e8650e'
        }
        return color_map.get(color, color)

    def lighten_color(self, color):
        """تفتيح اللون"""
        color_map = {
            '#28a745': '#34ce57',
            '#17a2b8': '#20c9e7',
            '#ffc107': '#ffcd39',
            '#dc3545': '#e4606d',
            '#6f42c1': '#8662d0',
            '#fd7e14': '#fd9644'
        }
        return color_map.get(color, color)

    def update_hijri(self):
        d = self.date_edit.date()
        if HIJRI_AVAILABLE:
            try:
                g = Gregorian(d.year(), d.month(), d.day()).to_hijri()
                # تنسيق التاريخ الهجري بالأرقام مع تحسين الشكل
                hijri_text = f'{g.day:02d}/{g.month:02d}/{g.year} هـ'
                self.lbl_hijri.setText(hijri_text)
            except Exception:
                self.lbl_hijri.setText('غير متوفر')
        else:
            self.lbl_hijri.setText('غير متوفر')

    def reload_students_if_loaded(self):
        """إعادة تحميل الطلاب إذا كانوا محملين بالفعل عند تغيير التاريخ"""
        if self.table.rowCount() > 0:
            self.load_students()

    def create_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            backup_dir = QtWidgets.QFileDialog.getExistingDirectory(
                self, 'اختر مجلد الحفظ', ''
            )
            if not backup_dir:
                return

            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = os.path.join(backup_dir, f'attendance_backup_{timestamp}.db')

            # نسخ قاعدة البيانات
            import shutil
            if os.path.exists(config.DB_FILE_PLAIN):
                shutil.copy2(config.DB_FILE_PLAIN, backup_file)
            elif os.path.exists(config.DB_FILE_ENCRYPTED):
                shutil.copy2(config.DB_FILE_ENCRYPTED, backup_file + '.enc')

            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح', f'تم إنشاء النسخة الاحتياطية:\n{backup_file}'
            )
        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في إنشاء النسخة الاحتياطية:\n{str(e)}'
            )

    def show_statistics(self):
        """عرض الإحصائيات المرئية"""
        stats_dialog = StatisticsDialog(self)
        stats_dialog.exec()

    def show_school_settings(self):
        """عرض نافذة إعدادات المدرسة"""
        settings_dialog = SchoolSettingsDialog(self)
        if settings_dialog.exec() == QtWidgets.QDialog.Accepted:
            # حفظ الإعدادات
            settings = settings_dialog.get_settings()
            self.save_school_settings(settings)

    def save_school_settings(self, settings):
        """حفظ إعدادات المدرسة"""
        try:
            import json
            with open('school_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح', 'تم حفظ إعدادات المدرسة'
            )
        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في حفظ الإعدادات:\n{str(e)}'
            )

    def load_school_settings(self):
        """تحميل إعدادات المدرسة"""
        try:
            import json
            with open('school_settings.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # إعدادات افتراضية
            return {
                'school_name': 'اسم المدرسة',
                'education_department': 'الإدارة العامة للتعليم',
                'education_office': 'مكتب التعليم',
                'logo_path': ''
            }
        except Exception:
            return {
                'school_name': 'اسم المدرسة',
                'education_department': 'الإدارة العامة للتعليم',
                'education_office': 'مكتب التعليم',
                'logo_path': ''
            }

    def populate_grade_section(self):
        conn = get_connection()
        cur = conn.cursor()
        cur.execute('SELECT DISTINCT grade FROM students;')
        grades = [r[0] for r in cur.fetchall()]
        cur.execute('SELECT DISTINCT section FROM students;')
        secs = [r[0] for r in cur.fetchall()]
        conn.close()
        self.cb_grade.clear()
        self.cb_grade.addItems(grades)
        self.cb_section.clear()
        self.cb_section.addItems(secs)

    def import_excel(self):
        path, _ = QtWidgets.QFileDialog.getOpenFileName(
            self, 'اختر ملف Excel', '', 'Excel Files (*.xlsx *.xls)'
        )
        if not path:
            return
        engine = 'openpyxl' if path.lower().endswith('.xlsx') else 'xlrd'
        df = pd.read_excel(path, sheet_name='Sheet2', usecols='C:F', engine=engine)
        df.columns = ['Section', 'GradeCode', 'Name', 'NationalID']
        df.dropna(subset=['Name'], inplace=True)
        grade_map = {'0725': 'أول متوسط', '0825': 'ثاني متوسط', '0925': 'ثالث متوسط'}
        df['Grade'] = df['GradeCode'].astype(str).map(grade_map)

        # تنظيف البيانات وإزالة الصفوف التي تحتوي على قيم فارغة
        df = df.dropna(subset=['Name', 'Grade', 'Section'])
        df = df[df['Grade'].notna() & df['Section'].notna()]

        conn = get_connection()
        cur = conn.cursor()
        cur.execute('DELETE FROM students;')

        successful_imports = 0
        for _, row in df.iterrows():
            try:
                if row['Name'] and row['Grade'] and row['Section']:
                    cur.execute(
                        'INSERT INTO students (name, grade, section) VALUES (?, ?, ?);',
                        (str(row['Name']).strip(), str(row['Grade']).strip(), str(row['Section']).strip())
                    )
                    successful_imports += 1
            except Exception as e:
                print(f"خطأ في إدراج الطالب {row['Name']}: {e}")
                continue
        conn.commit()
        conn.close()
        self.populate_grade_section()
        QtWidgets.QMessageBox.information(
            self, '✅ تم بنجاح',
            f'تم استيراد {successful_imports} طالب بنجاح من أصل {len(df)} سجل'
        )

    def load_students(self):
        grade = self.cb_grade.currentText()
        section = self.cb_section.currentText()

        if not grade or not section:
            QtWidgets.QMessageBox.warning(
                self, '⚠️ تحذير', 'يرجى اختيار المرحلة والفصل أولاً'
            )
            return

        # الحصول على التاريخ المحدد
        selected_date = self.date_edit.date().toString('yyyy-MM-dd')

        conn = get_connection()
        cur = conn.cursor()

        # جلب الطلاب مع إحصائياتهم وحالة الغياب للتاريخ المحدد
        cur.execute("""
            SELECT s.id, s.name,
                   COUNT(a.id) as total_days,
                   SUM(CASE WHEN a.status='absent' THEN 1 ELSE 0 END) as absent_days,
                   MAX(CASE WHEN a.date = ? AND a.status = 'absent' THEN 1 ELSE 0 END) as is_absent_today
            FROM students s
            LEFT JOIN attendance a ON s.id = a.student_id
            WHERE s.grade=? AND s.section=?
            GROUP BY s.id, s.name
            ORDER BY s.name
        """, (selected_date, grade, section))

        rows = cur.fetchall()
        conn.close()

        if not rows:
            QtWidgets.QMessageBox.information(
                self, 'ℹ️ معلومات', 'لا يوجد طلاب في هذا الفصل'
            )
            return

        self.table.setRowCount(len(rows))
        for i, (sid, name, total_days, absent_days, is_absent_today) in enumerate(rows):
            # رقم الطالب
            self.table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(i+1)))

            # اسم الطالب مع تنسيق محسن
            name_item = QtWidgets.QTableWidgetItem(name)
            name_item.setData(QtCore.Qt.UserRole, sid)

            # تحسين تنسيق اسم الطالب
            font = QtGui.QFont()
            font.setPointSize(16)  # حجم خط أكبر
            font.setBold(True)     # خط عريض
            name_item.setFont(font)

            # لون النص
            name_item.setForeground(QtGui.QColor('#2c3e50'))

            # محاذاة النص
            name_item.setTextAlignment(QtCore.Qt.AlignCenter | QtCore.Qt.AlignVCenter)

            self.table.setItem(i, 1, name_item)

            # خانة الغياب - إنشاء checkbox widget
            checkbox = QtWidgets.QCheckBox()
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 16px;
                    font-weight: bold;
                }
                QCheckBox::indicator {
                    width: 20px;
                    height: 20px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #4a90e2;
                    background-color: white;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #dc3545;
                    background-color: #dc3545;
                    border-radius: 3px;
                }
            """)

            # تعيين حالة الغياب بناءً على البيانات المحفوظة
            if is_absent_today:
                checkbox.setChecked(True)

            # وضع الـ checkbox في الخلية
            self.table.setCellWidget(i, 2, checkbox)

            # الإحصائيات
            absent_days = absent_days or 0
            total_days = total_days or 0
            percentage = (absent_days / total_days * 100) if total_days > 0 else 0
            stats_text = f'غاب {absent_days} من {total_days} يوم ({percentage:.1f}%)'

            stats_item = QtWidgets.QTableWidgetItem(stats_text)
            if percentage > 20:
                stats_item.setBackground(QtGui.QColor('#ffebee'))  # أحمر فاتح
            elif percentage > 10:
                stats_item.setBackground(QtGui.QColor('#fff3e0'))  # برتقالي فاتح
            else:
                stats_item.setBackground(QtGui.QColor('#e8f5e8'))  # أخضر فاتح

            self.table.setItem(i, 3, stats_item)

        # تحديث عرض الأعمدة وارتفاع الصفوف
        self.table.resizeColumnsToContents()

        # تعيين ارتفاع أكبر للصفوف
        for row in range(self.table.rowCount()):
            self.table.setRowHeight(row, 50)  # ارتفاع أكبر للصفوف

        QtWidgets.QMessageBox.information(
            self, '✅ تم بنجاح', f'تم تحميل {len(rows)} طالب من {grade} - {section}'
        )

    def save_attendance(self):
        if self.table.rowCount() == 0:
            QtWidgets.QMessageBox.warning(
                self, '⚠️ تحذير', 'لا يوجد طلاب لحفظ غيابهم. يرجى تحميل قائمة الطلاب أولاً.'
            )
            return

        date_str = self.date_edit.date().toString('yyyy-MM-dd')

        # التحقق من وجود سجلات سابقة لنفس التاريخ
        conn = get_connection()
        cur = conn.cursor()

        # جمع معرفات الطلاب
        student_ids = []
        for i in range(self.table.rowCount()):
            sid = self.table.item(i, 1).data(QtCore.Qt.UserRole)
            student_ids.append(sid)

        if student_ids:
            placeholders = ','.join(['?'] * len(student_ids))
            cur.execute(f"""
                SELECT COUNT(*) FROM attendance
                WHERE date = ? AND student_id IN ({placeholders})
            """, [date_str] + student_ids)

            existing_count = cur.fetchone()[0]

            if existing_count > 0:
                reply = QtWidgets.QMessageBox.question(
                    self, '🤔 تأكيد',
                    f'يوجد {existing_count} سجل غياب مسجل مسبقاً لهذا التاريخ.\nهل تريد تحديث السجلات الموجودة؟',
                    QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No
                )

                if reply == QtWidgets.QMessageBox.No:
                    conn.close()
                    return

            # حذف جميع السجلات الموجودة للطلاب الحاليين في هذا التاريخ
            cur.execute(f"""
                DELETE FROM attendance
                WHERE date = ? AND student_id IN ({placeholders})
            """, [date_str] + student_ids)

        # حفظ السجلات الجديدة
        saved_count = 0
        absent_count = 0

        for i in range(self.table.rowCount()):
            sid = self.table.item(i, 1).data(QtCore.Qt.UserRole)
            student_name = self.table.item(i, 1).text()  # اسم الطالب من العمود الثاني

            # فحص حالة الغياب من checkbox
            checkbox = self.table.cellWidget(i, 2)  # العمود الثالث للـ checkbox
            if checkbox and checkbox.isChecked():
                # طالب غائب - إضافة سجل غياب
                cur.execute(
                    'INSERT INTO attendance (student_id, date, status) VALUES (?, ?, ?)',
                    (sid, date_str, 'absent')
                )
                absent_count += 1
                saved_count += 1

        conn.commit()
        conn.close()

        # إعادة تحميل الطلاب لتحديث الإحصائيات
        self.load_students()

        QtWidgets.QMessageBox.information(
            self, '✅ تم بنجاح',
            f'تم حفظ الغياب بتاريخ {date_str}\n'
            f'عدد الغائبين: {absent_count}\n'
            f'عدد الحاضرين: {self.table.rowCount() - absent_count}'
        )

    def show_student_report(self):
        """عرض تقرير طالب احترافي مفصل"""
        grade = self.cb_grade.currentText()
        section = self.cb_section.currentText()
        conn = get_connection()
        cur = conn.cursor()
        cur.execute(
            'SELECT name FROM students WHERE grade=? AND section=? ORDER BY name;', (grade, section)
        )
        names = [r[0] for r in cur.fetchall()]
        conn.close()
        if not names:
            QtWidgets.QMessageBox.information(self, 'تنبيه', 'لا يوجد طلاب لهذا الفصل')
            return
        name, ok = QtWidgets.QInputDialog.getItem(
            self, 'تقرير طالب', 'اختر الطالب:', names, 0, False
        )
        if not ok:
            return

        self.create_professional_student_report(name)

    def create_professional_student_report(self, student_name):
        """إنشاء تقرير طالب احترافي مفصل"""
        try:
            conn = get_connection()
            cur = conn.cursor()

            # جلب معلومات الطالب
            cur.execute(
                'SELECT id, name, grade, section FROM students WHERE name=?', (student_name,)
            )
            student_info = cur.fetchone()
            if not student_info:
                QtWidgets.QMessageBox.warning(self, 'تحذير', 'لم يتم العثور على الطالب')
                return

            student_id, name, grade, section = student_info

            # جلب سجلات الغياب
            cur.execute('''
                SELECT date, status
                FROM attendance
                WHERE student_id = ? AND status = 'absent'
                ORDER BY date DESC
            ''', (student_id,))
            absence_records = cur.fetchall()

            # حساب الإحصائيات
            cur.execute('''
                SELECT COUNT(*) as total_days,
                       SUM(CASE WHEN status='absent' THEN 1 ELSE 0 END) as absent_days
                FROM attendance
                WHERE student_id = ?
            ''', (student_id,))
            stats = cur.fetchone()
            total_days, absent_days = stats if stats else (0, 0)
            present_days = total_days - absent_days
            absence_percentage = (absent_days / total_days * 100) if total_days > 0 else 0

            conn.close()

            # إنشاء نافذة التقرير
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle(f'📋 تقرير غياب الطالب - {name}')
            dialog.setLayoutDirection(QtCore.Qt.RightToLeft)
            dialog.resize(900, 700)

            layout = QtWidgets.QVBoxLayout(dialog)
            layout.setSpacing(20)
            layout.setContentsMargins(20, 20, 20, 20)

            # رأس التقرير
            header_frame = QtWidgets.QFrame()
            header_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #667eea, stop:1 #764ba2);
                    border-radius: 12px;
                    padding: 20px;
                    margin: 10px;
                }
            """)
            header_layout = QtWidgets.QVBoxLayout(header_frame)

            # عنوان التقرير
            title_label = QtWidgets.QLabel(f'📋 تقرير غياب الطالب')
            title_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 24px;
                    font-weight: bold;
                    text-align: center;
                    margin: 10px;
                }
            """)
            title_label.setAlignment(QtCore.Qt.AlignCenter)
            header_layout.addWidget(title_label)

            # معلومات الطالب
            student_info_label = QtWidgets.QLabel(f'👤 الطالب: {name} | 🏫 الفصل: {grade} - {section}')
            student_info_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    margin: 5px;
                }
            """)
            student_info_label.setAlignment(QtCore.Qt.AlignCenter)
            header_layout.addWidget(student_info_label)

            layout.addWidget(header_frame)

            # بطاقات الإحصائيات
            stats_frame = QtWidgets.QFrame()
            stats_layout = QtWidgets.QHBoxLayout(stats_frame)
            stats_layout.setSpacing(15)

            # بطاقة إجمالي الأيام
            self.create_stat_card_for_report(stats_layout, "📅 إجمالي الأيام", str(total_days), "#17a2b8")

            # بطاقة أيام الحضور
            self.create_stat_card_for_report(stats_layout, "✅ أيام الحضور", str(present_days), "#28a745")

            # بطاقة أيام الغياب
            self.create_stat_card_for_report(stats_layout, "❌ أيام الغياب", str(absent_days), "#dc3545")

            # بطاقة نسبة الغياب
            percentage_color = "#dc3545" if absence_percentage > 20 else "#ffc107" if absence_percentage > 10 else "#28a745"
            self.create_stat_card_for_report(stats_layout, "📊 نسبة الغياب", f"{absence_percentage:.1f}%", percentage_color)

            layout.addWidget(stats_frame)

            # جدول تفاصيل الغياب
            if absence_records:
                table_label = QtWidgets.QLabel(f'📋 تفاصيل أيام الغياب ({len(absence_records)} يوم)')
                table_label.setStyleSheet("""
                    QLabel {
                        font-size: 18px;
                        font-weight: bold;
                        color: #2c3e50;
                        margin: 10px 0;
                    }
                """)
                layout.addWidget(table_label)

                # إنشاء الجدول
                table = QtWidgets.QTableWidget(len(absence_records), 3)
                table.setHorizontalHeaderLabels(['#', 'التاريخ الميلادي', 'التاريخ الهجري'])
                table.setLayoutDirection(QtCore.Qt.RightToLeft)

                # تنسيق الجدول
                table.setStyleSheet("""
                    QTableWidget {
                        gridline-color: #ddd;
                        background-color: white;
                        alternate-background-color: #f8f9fa;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                    }
                    QHeaderView::section {
                        background-color: #343a40;
                        color: white;
                        padding: 10px;
                        border: none;
                        font-weight: bold;
                        font-size: 14px;
                    }
                    QTableWidget::item {
                        padding: 8px;
                        border-bottom: 1px solid #eee;
                    }
                """)

                table.setAlternatingRowColors(True)
                table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)
                table.verticalHeader().setVisible(False)
                table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)

                # ملء البيانات
                for i, (date, status) in enumerate(absence_records):
                    # رقم متسلسل
                    table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(i + 1)))

                    # التاريخ الميلادي
                    table.setItem(i, 1, QtWidgets.QTableWidgetItem(date))

                    # التاريخ الهجري
                    y, m, d = map(int, date.split('-'))
                    g = Gregorian(y, m, d).to_hijri()
                    hijri_numeric = f"{g.day:02d}/{g.month:02d}/{g.year}"
                    table.setItem(i, 2, QtWidgets.QTableWidgetItem(hijri_numeric))

                    # تنسيق الصفوف
                    for j in range(3):
                        item = table.item(i, j)
                        if item:
                            item.setTextAlignment(QtCore.Qt.AlignCenter)
                            if j == 0:  # رقم متسلسل
                                item.setBackground(QtGui.QColor('#f8f9fa'))
                                font = item.font()
                                font.setBold(True)
                                item.setFont(font)

                layout.addWidget(table)

                # أزرار الحفظ والطباعة
                self.add_save_print_buttons(layout, f'تقرير غياب {name}', table)
            else:
                # رسالة عدم وجود غياب
                no_absence_label = QtWidgets.QLabel('🎉 ممتاز! لا يوجد أيام غياب لهذا الطالب')
                no_absence_label.setStyleSheet("""
                    QLabel {
                        background-color: #d4edda;
                        color: #155724;
                        border: 2px solid #c3e6cb;
                        border-radius: 8px;
                        padding: 20px;
                        font-size: 18px;
                        font-weight: bold;
                        text-align: center;
                        margin: 20px;
                    }
                """)
                no_absence_label.setAlignment(QtCore.Qt.AlignCenter)
                layout.addWidget(no_absence_label)

            dialog.exec()

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في إنشاء تقرير الطالب:\n{str(e)}'
            )

    def create_stat_card_for_report(self, layout, title, value, color):
        """إنشاء بطاقة إحصائية للتقرير"""
        card = QtWidgets.QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
                min-height: 80px;
            }}
        """)

        card_layout = QtWidgets.QVBoxLayout(card)
        card_layout.setAlignment(QtCore.Qt.AlignCenter)

        # العنوان
        title_label = QtWidgets.QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: bold;
                text-align: center;
            }
        """)
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        card_layout.addWidget(title_label)

        # القيمة
        value_label = QtWidgets.QLabel(value)
        value_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                text-align: center;
                margin-top: 5px;
            }
        """)
        value_label.setAlignment(QtCore.Qt.AlignCenter)
        card_layout.addWidget(value_label)

        layout.addWidget(card)

    def show_class_report(self):
        """عرض تقرير فصل احترافي مفصل"""
        grade = self.cb_grade.currentText()
        section = self.cb_section.currentText()
        self.create_professional_class_report(grade, section)

    def create_professional_class_report(self, grade, section):
        """إنشاء تقرير فصل احترافي مفصل"""
        try:
            conn = get_connection()
            cur = conn.cursor()

            # جلب طلاب الفصل مع إحصائياتهم
            cur.execute('''
                SELECT s.id, s.name,
                       COUNT(a.id) as total_days,
                       SUM(CASE WHEN a.status='absent' THEN 1 ELSE 0 END) as absent_days
                FROM students s
                LEFT JOIN attendance a ON s.id = a.student_id
                WHERE s.grade=? AND s.section=?
                GROUP BY s.id, s.name
                ORDER BY s.name
            ''', (grade, section))
            students_data = cur.fetchall()

            if not students_data:
                QtWidgets.QMessageBox.information(
                    self, 'تنبيه', f'لا يوجد طلاب في {grade} - {section}'
                )
                conn.close()
                return

            # حساب إحصائيات الفصل
            total_students = len(students_data)
            total_absent_days = sum(row[3] or 0 for row in students_data)
            total_recorded_days = sum(row[2] or 0 for row in students_data)
            class_absence_rate = (total_absent_days / total_recorded_days * 100) if total_recorded_days > 0 else 0

            conn.close()

            # إنشاء نافذة التقرير
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle(f'📊 تقرير الفصل - {grade} - {section}')
            dialog.setLayoutDirection(QtCore.Qt.RightToLeft)
            dialog.resize(1000, 800)

            layout = QtWidgets.QVBoxLayout(dialog)
            layout.setSpacing(20)
            layout.setContentsMargins(20, 20, 20, 20)

            # رأس التقرير
            header_frame = QtWidgets.QFrame()
            header_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #ff9a9e, stop:1 #fecfef);
                    border-radius: 12px;
                    padding: 20px;
                    margin: 10px;
                }
            """)
            header_layout = QtWidgets.QVBoxLayout(header_frame)

            # عنوان التقرير
            title_label = QtWidgets.QLabel(f'📊 تقرير الفصل')
            title_label.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    font-size: 24px;
                    font-weight: bold;
                    text-align: center;
                    margin: 10px;
                }
            """)
            title_label.setAlignment(QtCore.Qt.AlignCenter)
            header_layout.addWidget(title_label)

            # معلومات الفصل
            class_info_label = QtWidgets.QLabel(f'🏫 الفصل: {grade} - {section} | 👥 عدد الطلاب: {total_students}')
            class_info_label.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    margin: 5px;
                }
            """)
            class_info_label.setAlignment(QtCore.Qt.AlignCenter)
            header_layout.addWidget(class_info_label)

            layout.addWidget(header_frame)

            # بطاقات الإحصائيات
            stats_frame = QtWidgets.QFrame()
            stats_layout = QtWidgets.QHBoxLayout(stats_frame)
            stats_layout.setSpacing(15)

            # بطاقة عدد الطلاب
            self.create_stat_card_for_report(stats_layout, "👥 عدد الطلاب", str(total_students), "#17a2b8")

            # بطاقة إجمالي أيام الغياب
            self.create_stat_card_for_report(stats_layout, "❌ إجمالي الغياب", str(total_absent_days), "#dc3545")

            # بطاقة معدل الغياب
            rate_color = "#dc3545" if class_absence_rate > 15 else "#ffc107" if class_absence_rate > 8 else "#28a745"
            self.create_stat_card_for_report(stats_layout, "📊 معدل الغياب", f"{class_absence_rate:.1f}%", rate_color)

            layout.addWidget(stats_frame)

            # جدول الطلاب
            table_label = QtWidgets.QLabel(f'📋 قائمة طلاب الفصل وإحصائيات الغياب')
            table_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 10px 0;
                }
            """)
            layout.addWidget(table_label)

            # إنشاء الجدول
            table = QtWidgets.QTableWidget(len(students_data), 5)
            table.setHorizontalHeaderLabels(['#', 'اسم الطالب', 'إجمالي الأيام', 'أيام الغياب', 'نسبة الغياب'])
            table.setLayoutDirection(QtCore.Qt.RightToLeft)

            # تنسيق الجدول
            table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #ddd;
                    background-color: white;
                    alternate-background-color: #f8f9fa;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                }
                QHeaderView::section {
                    background-color: #343a40;
                    color: white;
                    padding: 10px;
                    border: none;
                    font-weight: bold;
                    font-size: 14px;
                }
                QTableWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #eee;
                }
            """)

            table.setAlternatingRowColors(True)
            table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)
            table.verticalHeader().setVisible(False)
            table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)

            # ملء البيانات
            for i, (student_id, name, total_days, absent_days) in enumerate(students_data):
                total_days = total_days or 0
                absent_days = absent_days or 0
                absence_percentage = (absent_days / total_days * 100) if total_days > 0 else 0

                # رقم متسلسل
                table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(i + 1)))

                # اسم الطالب
                name_item = QtWidgets.QTableWidgetItem(name)
                name_item.setFont(QtGui.QFont("Arial", 12, QtGui.QFont.Bold))
                table.setItem(i, 1, name_item)

                # إجمالي الأيام
                table.setItem(i, 2, QtWidgets.QTableWidgetItem(str(total_days)))

                # أيام الغياب
                absent_item = QtWidgets.QTableWidgetItem(str(absent_days))
                if absent_days > 0:
                    absent_item.setBackground(QtGui.QColor('#ffebee'))
                table.setItem(i, 3, absent_item)

                # نسبة الغياب
                percentage_item = QtWidgets.QTableWidgetItem(f"{absence_percentage:.1f}%")
                if absence_percentage > 20:
                    percentage_item.setBackground(QtGui.QColor('#ffcdd2'))
                elif absence_percentage > 10:
                    percentage_item.setBackground(QtGui.QColor('#fff3e0'))
                else:
                    percentage_item.setBackground(QtGui.QColor('#e8f5e8'))
                table.setItem(i, 4, percentage_item)

                # تنسيق الصفوف
                for j in range(5):
                    item = table.item(i, j)
                    if item:
                        item.setTextAlignment(QtCore.Qt.AlignCenter)
                        if j == 0:  # رقم متسلسل
                            item.setBackground(QtGui.QColor('#f8f9fa'))
                            font = item.font()
                            font.setBold(True)
                            item.setFont(font)

            layout.addWidget(table)

            # أزرار الحفظ والطباعة
            self.add_save_print_buttons(layout, f'تقرير فصل {grade} - {section}', table)

            dialog.exec()

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في إنشاء تقرير الفصل:\n{str(e)}'
            )

    def show_school_report(self):
        """عرض تقرير المدرسة الاحترافي المفصل"""
        self.create_professional_school_report()

    def create_professional_school_report(self):
        """إنشاء تقرير المدرسة الاحترافي الشامل"""
        try:
            conn = get_connection()
            cur = conn.cursor()

            # إحصائيات عامة للمدرسة
            cur.execute('''
                SELECT
                    COUNT(DISTINCT s.id) as total_students,
                    COUNT(DISTINCT s.grade) as total_grades,
                    COUNT(DISTINCT CONCAT(s.grade, '-', s.section)) as total_classes,
                    COUNT(a.id) as total_attendance_records,
                    SUM(CASE WHEN a.status='absent' THEN 1 ELSE 0 END) as total_absent_days
                FROM students s
                LEFT JOIN attendance a ON s.id = a.student_id
            ''')
            school_stats = cur.fetchone()
            total_students, total_grades, total_classes, total_records, total_absent = school_stats

            # إحصائيات حسب الفصول
            cur.execute('''
                SELECT s.grade, s.section,
                       COUNT(DISTINCT s.id) as student_count,
                       COUNT(a.id) as total_days,
                       SUM(CASE WHEN a.status='absent' THEN 1 ELSE 0 END) as absent_days
                FROM students s
                LEFT JOIN attendance a ON s.id = a.student_id
                GROUP BY s.grade, s.section
                ORDER BY s.grade, s.section
            ''')
            class_stats = cur.fetchall()

            # أكثر الطلاب غياباً
            cur.execute('''
                SELECT s.name, s.grade, s.section,
                       COUNT(a.id) as total_days,
                       SUM(CASE WHEN a.status='absent' THEN 1 ELSE 0 END) as absent_days
                FROM students s
                LEFT JOIN attendance a ON s.id = a.student_id
                GROUP BY s.id, s.name, s.grade, s.section
                HAVING absent_days > 0
                ORDER BY absent_days DESC
                LIMIT 10
            ''')
            top_absent_students = cur.fetchall()

            conn.close()

            # إنشاء نافذة التقرير
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle('🏫 تقرير المدرسة الشامل')
            dialog.setLayoutDirection(QtCore.Qt.RightToLeft)
            dialog.resize(1200, 900)

            layout = QtWidgets.QVBoxLayout(dialog)
            layout.setSpacing(20)
            layout.setContentsMargins(20, 20, 20, 20)

            # رأس التقرير
            header_frame = QtWidgets.QFrame()
            header_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #667eea, stop:1 #764ba2);
                    border-radius: 12px;
                    padding: 20px;
                    margin: 10px;
                }
            """)
            header_layout = QtWidgets.QVBoxLayout(header_frame)

            # عنوان التقرير
            title_label = QtWidgets.QLabel('🏫 تقرير المدرسة الشامل')
            title_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 28px;
                    font-weight: bold;
                    text-align: center;
                    margin: 10px;
                }
            """)
            title_label.setAlignment(QtCore.Qt.AlignCenter)
            header_layout.addWidget(title_label)

            # معلومات المدرسة
            school_settings = self.load_school_settings()
            school_info = f"🏫 {school_settings['school_name']}"
            if school_settings['education_office']:
                school_info += f" | 📍 {school_settings['education_office']}"

            school_info_label = QtWidgets.QLabel(school_info)
            school_info_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    margin: 5px;
                }
            """)
            school_info_label.setAlignment(QtCore.Qt.AlignCenter)
            header_layout.addWidget(school_info_label)

            layout.addWidget(header_frame)

            # بطاقات الإحصائيات العامة
            stats_frame = QtWidgets.QFrame()
            stats_layout = QtWidgets.QHBoxLayout(stats_frame)
            stats_layout.setSpacing(15)

            # بطاقة عدد الطلاب
            self.create_stat_card_for_report(stats_layout, "👥 إجمالي الطلاب", str(total_students or 0), "#17a2b8")

            # بطاقة عدد الفصول
            self.create_stat_card_for_report(stats_layout, "🏫 عدد الفصول", str(total_classes or 0), "#28a745")

            # بطاقة إجمالي الغياب
            self.create_stat_card_for_report(stats_layout, "❌ إجمالي الغياب", str(total_absent or 0), "#dc3545")

            # بطاقة معدل الغياب
            overall_rate = (total_absent / total_records * 100) if total_records and total_records > 0 else 0
            rate_color = "#dc3545" if overall_rate > 15 else "#ffc107" if overall_rate > 8 else "#28a745"
            self.create_stat_card_for_report(stats_layout, "📊 معدل الغياب", f"{overall_rate:.1f}%", rate_color)

            layout.addWidget(stats_frame)

            # إنشاء تبويبات للتقارير المختلفة
            tab_widget = QtWidgets.QTabWidget()
            tab_widget.setLayoutDirection(QtCore.Qt.RightToLeft)
            tab_widget.setStyleSheet("""
                QTabWidget::pane {
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    background-color: white;
                }
                QTabBar::tab {
                    background-color: #f8f9fa;
                    padding: 10px 20px;
                    margin: 2px;
                    border-radius: 6px;
                    font-weight: bold;
                }
                QTabBar::tab:selected {
                    background-color: #007bff;
                    color: white;
                }
            """)

            # تبويب إحصائيات الفصول
            self.create_class_stats_tab(tab_widget, class_stats)

            # تبويب أكثر الطلاب غياباً
            self.create_top_absent_tab(tab_widget, top_absent_students)

            layout.addWidget(tab_widget)

            dialog.exec()

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في إنشاء تقرير المدرسة:\n{str(e)}'
            )

    def create_class_stats_tab(self, tab_widget, class_stats):
        """إنشاء تبويب إحصائيات الفصول"""
        tab = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان التبويب
        title_label = QtWidgets.QLabel('📊 إحصائيات الفصول')
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin: 10px 0;
            }
        """)
        layout.addWidget(title_label)

        if class_stats:
            # إنشاء الجدول
            table = QtWidgets.QTableWidget(len(class_stats), 6)
            table.setHorizontalHeaderLabels(['#', 'المرحلة', 'الفصل', 'عدد الطلاب', 'أيام الغياب', 'معدل الغياب'])
            table.setLayoutDirection(QtCore.Qt.RightToLeft)

            # تنسيق الجدول
            table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #ddd;
                    background-color: white;
                    alternate-background-color: #f8f9fa;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                }
                QHeaderView::section {
                    background-color: #343a40;
                    color: white;
                    padding: 10px;
                    border: none;
                    font-weight: bold;
                    font-size: 14px;
                }
                QTableWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #eee;
                }
            """)

            table.setAlternatingRowColors(True)
            table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)
            table.verticalHeader().setVisible(False)
            table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)

            # ملء البيانات
            for i, (grade, section, student_count, total_days, absent_days) in enumerate(class_stats):
                student_count = student_count or 0
                total_days = total_days or 0
                absent_days = absent_days or 0
                absence_rate = (absent_days / total_days * 100) if total_days > 0 else 0

                # رقم متسلسل
                table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(i + 1)))

                # المرحلة
                table.setItem(i, 1, QtWidgets.QTableWidgetItem(grade or ''))

                # الفصل
                table.setItem(i, 2, QtWidgets.QTableWidgetItem(section or ''))

                # عدد الطلاب
                table.setItem(i, 3, QtWidgets.QTableWidgetItem(str(student_count)))

                # أيام الغياب
                absent_item = QtWidgets.QTableWidgetItem(str(absent_days))
                if absent_days > 0:
                    absent_item.setBackground(QtGui.QColor('#ffebee'))
                table.setItem(i, 4, absent_item)

                # معدل الغياب
                rate_item = QtWidgets.QTableWidgetItem(f"{absence_rate:.1f}%")
                if absence_rate > 20:
                    rate_item.setBackground(QtGui.QColor('#ffcdd2'))
                elif absence_rate > 10:
                    rate_item.setBackground(QtGui.QColor('#fff3e0'))
                else:
                    rate_item.setBackground(QtGui.QColor('#e8f5e8'))
                table.setItem(i, 5, rate_item)

                # تنسيق الصفوف
                for j in range(6):
                    item = table.item(i, j)
                    if item:
                        item.setTextAlignment(QtCore.Qt.AlignCenter)
                        if j == 0:  # رقم متسلسل
                            item.setBackground(QtGui.QColor('#f8f9fa'))
                            font = item.font()
                            font.setBold(True)
                            item.setFont(font)

            layout.addWidget(table)

            # أزرار الحفظ والطباعة
            self.add_save_print_buttons(layout, 'إحصائيات الفصول', table)
        else:
            # رسالة عدم وجود بيانات
            no_data_label = QtWidgets.QLabel('📊 لا توجد بيانات إحصائية للفصول')
            no_data_label.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    color: #6c757d;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    padding: 40px;
                    font-size: 16px;
                    text-align: center;
                }
            """)
            no_data_label.setAlignment(QtCore.Qt.AlignCenter)
            layout.addWidget(no_data_label)

        tab_widget.addTab(tab, '📊 إحصائيات الفصول')

    def create_top_absent_tab(self, tab_widget, top_absent_students):
        """إنشاء تبويب أكثر الطلاب غياباً"""
        tab = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان التبويب
        title_label = QtWidgets.QLabel('🔴 أكثر الطلاب غياباً')
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #dc3545;
                margin: 10px 0;
            }
        """)
        layout.addWidget(title_label)

        if top_absent_students:
            # إنشاء الجدول
            table = QtWidgets.QTableWidget(len(top_absent_students), 6)
            table.setHorizontalHeaderLabels(['الترتيب', 'اسم الطالب', 'المرحلة', 'الفصل', 'أيام الغياب', 'نسبة الغياب'])
            table.setLayoutDirection(QtCore.Qt.RightToLeft)

            # تنسيق الجدول
            table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #ddd;
                    background-color: white;
                    alternate-background-color: #fff5f5;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                }
                QHeaderView::section {
                    background-color: #dc3545;
                    color: white;
                    padding: 10px;
                    border: none;
                    font-weight: bold;
                    font-size: 14px;
                }
                QTableWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #eee;
                }
            """)

            table.setAlternatingRowColors(True)
            table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)
            table.verticalHeader().setVisible(False)
            table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)

            # ملء البيانات
            for i, (name, grade, section, total_days, absent_days) in enumerate(top_absent_students):
                total_days = total_days or 0
                absent_days = absent_days or 0
                absence_percentage = (absent_days / total_days * 100) if total_days > 0 else 0

                # الترتيب
                rank_item = QtWidgets.QTableWidgetItem(str(i + 1))
                rank_item.setBackground(QtGui.QColor('#f8f9fa'))
                font = rank_item.font()
                font.setBold(True)
                rank_item.setFont(font)
                table.setItem(i, 0, rank_item)

                # اسم الطالب
                name_item = QtWidgets.QTableWidgetItem(name)
                name_item.setFont(QtGui.QFont("Arial", 12, QtGui.QFont.Bold))
                table.setItem(i, 1, name_item)

                # المرحلة
                table.setItem(i, 2, QtWidgets.QTableWidgetItem(grade or ''))

                # الفصل
                table.setItem(i, 3, QtWidgets.QTableWidgetItem(section or ''))

                # أيام الغياب
                absent_item = QtWidgets.QTableWidgetItem(str(absent_days))
                absent_item.setBackground(QtGui.QColor('#ffcdd2'))
                absent_item.setFont(QtGui.QFont("Arial", 12, QtGui.QFont.Bold))
                table.setItem(i, 4, absent_item)

                # نسبة الغياب
                percentage_item = QtWidgets.QTableWidgetItem(f"{absence_percentage:.1f}%")
                if absence_percentage > 30:
                    percentage_item.setBackground(QtGui.QColor('#f44336'))
                    percentage_item.setForeground(QtGui.QColor('white'))
                elif absence_percentage > 20:
                    percentage_item.setBackground(QtGui.QColor('#ff9800'))
                else:
                    percentage_item.setBackground(QtGui.QColor('#ffcdd2'))
                percentage_item.setFont(QtGui.QFont("Arial", 12, QtGui.QFont.Bold))
                table.setItem(i, 5, percentage_item)

                # تنسيق الصفوف
                for j in range(6):
                    item = table.item(i, j)
                    if item:
                        item.setTextAlignment(QtCore.Qt.AlignCenter)

            layout.addWidget(table)

            # أزرار الحفظ والطباعة
            self.add_save_print_buttons(layout, 'أكثر الطلاب غياباً', table)
        else:
            # رسالة عدم وجود بيانات
            no_data_label = QtWidgets.QLabel('🎉 ممتاز! لا يوجد طلاب بغياب مرتفع')
            no_data_label.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    color: #155724;
                    border: 2px solid #c3e6cb;
                    border-radius: 8px;
                    padding: 40px;
                    font-size: 16px;
                    text-align: center;
                }
            """)
            no_data_label.setAlignment(QtCore.Qt.AlignCenter)
            layout.addWidget(no_data_label)

        tab_widget.addTab(tab, '🔴 أكثر الطلاب غياباً')

    def show_reports(self):
        dlg = QtWidgets.QDialog(self)
        dlg.setLayoutDirection(QtCore.Qt.RightToLeft)
        dlg.setWindowTitle('التقارير المتقدمة')
        lay = QtWidgets.QVBoxLayout(dlg)
        types = QtWidgets.QComboBox()
        types.addItems(['مدرسة', 'فصل', 'صف'])
        lay.addWidget(types)
        gcb = QtWidgets.QComboBox()
        scb = QtWidgets.QComboBox()
        lay.addWidget(QtWidgets.QLabel('المرحلة:'))
        lay.addWidget(gcb)
        lay.addWidget(QtWidgets.QLabel('الفصل:'))
        lay.addWidget(scb)
        self.populate_grade_section()
        gcb.addItems([self.cb_grade.itemText(i) for i in range(self.cb_grade.count())])
        scb.addItems([self.cb_section.itemText(i) for i in range(self.cb_section.count())])
        btn = QtWidgets.QPushButton('تحميل التقرير')
        lay.addWidget(btn)
        tab = QtWidgets.QTabWidget()
        lay.addWidget(tab)

        def load():
            tab.clear()
            periods = [('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('yearly', 'سنوي')]
            for p, title in periods:
                sd, ed = period_range(p)
                where = ''
                params = ()
                idx = types.currentIndex()
                if idx == 1:
                    where = 'AND s.grade=?'
                    params = (gcb.currentText(),)
                elif idx == 2:
                    where = 'AND s.grade=? AND s.section=?'
                    params = (gcb.currentText(), scb.currentText())
                data = get_report(sd, ed, where, params)
                tbl = QtWidgets.QTableWidget(len(data), 4)
                tbl.setHorizontalHeaderLabels(['اسم', 'غياب', 'إجمالي', '%'])
                tbl.setLayoutDirection(QtCore.Qt.RightToLeft)
                for i, (n, a, t, pct) in enumerate(data):
                    for j, val in enumerate((n, a, t, pct)):
                        item = QtWidgets.QTableWidgetItem(str(val))
                        item.setTextAlignment(QtCore.Qt.AlignCenter)
                        tbl.setItem(i, j, item)
                tbl.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)
                self.add_save_print_buttons(tab, title, tbl)

        btn.clicked.connect(load)
        dlg.exec()

    def convert_hijri_to_numeric(self, hijri_text):
        """تحويل التاريخ الهجري من نص إلى أرقام"""
        if not hijri_text or hijri_text == 'غير متوفر':
            return 'غير متوفر'

        try:
            # إذا كان التاريخ يحتوي على أسماء الشهور
            for month_num, month_name in hijri_months.items():
                if month_name in hijri_text:
                    # استخراج اليوم والسنة
                    parts = hijri_text.split('/')
                    if len(parts) == 3:
                        day = parts[0]
                        year = parts[2]
                        return f"{day}/{month_num:02d}/{year}"

            # إذا كان التاريخ بالفعل بصيغة رقمية
            if '/' in hijri_text and len(hijri_text.split('/')) == 3:
                return hijri_text

        except:
            pass

        return hijri_text

    def fetch_records(self, name):
        """جلب سجلات الغياب للطالب مع تحويل التواريخ الهجرية إلى أرقام"""
        conn = get_connection()
        cur = conn.cursor()
        cur.execute(
            'SELECT date, status FROM attendance a JOIN students s ON s.id=a.student_id WHERE s.name=? AND a.status="absent" ORDER BY date DESC;', (name,)
        )
        rec = cur.fetchall()
        conn.close()
        rows = []
        for dt, st in rec:
            y, m, d = map(int, dt.split('-'))
            g = Gregorian(y, m, d).to_hijri()
            hijri_numeric = f"{g.day:02d}/{g.month:02d}/{g.year}"
            rows.append((dt, hijri_numeric, 'غائب'))
        return rows

    def display_table_dialog(self, data, headers, title):
        dlg = QtWidgets.QDialog(self)
        dlg.setLayoutDirection(QtCore.Qt.RightToLeft)
        dlg.setWindowTitle(title)
        lay = QtWidgets.QVBoxLayout(dlg)
        tbl = QtWidgets.QTableWidget(len(data), len(headers))
        tbl.setHorizontalHeaderLabels(headers)
        tbl.setLayoutDirection(QtCore.Qt.RightToLeft)
        for i, row in enumerate(data):
            for j, val in enumerate(row):
                tbl.setItem(i, j, QtWidgets.QTableWidgetItem(str(val)))
        tbl.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)
        lay.addWidget(tbl)
        self.add_save_print_buttons(lay, title, tbl)
        dlg.exec()

    def generate_and_show_report(self, where_clause, params, title):
        sd, ed = period_range('yearly')
        data = get_report(sd, ed, where_clause, params)
        self.display_table_dialog(data, ['اسم', 'غياب', 'إجمالي', '%'], title)

    def add_save_print_buttons(self, parent, title, widget):
        """إضافة أزرار الحفظ والطباعة المحسنة"""
        h = QtWidgets.QHBoxLayout()
        h.setSpacing(10)

        # زر حفظ PDF محسن
        btn_pdf = QtWidgets.QPushButton('📄 حفظ PDF')
        btn_pdf.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc3545, stop:1 #c82333);
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e4606d, stop:1 #dc3545);
            }
        """)
        btn_pdf.clicked.connect(lambda: self.save_pdf_advanced(widget, title))
        h.addWidget(btn_pdf)

        # زر حفظ Word محسن
        btn_doc = QtWidgets.QPushButton('📝 حفظ Word')
        btn_doc.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3395ff, stop:1 #007bff);
            }
        """)
        btn_doc.clicked.connect(lambda: self.save_word_advanced(widget, title))
        h.addWidget(btn_doc)

        # زر حفظ Excel
        btn_excel = QtWidgets.QPushButton('📊 حفظ Excel')
        btn_excel.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34ce57, stop:1 #28a745);
            }
        """)
        btn_excel.clicked.connect(lambda: self.save_excel_advanced(widget, title))
        h.addWidget(btn_excel)

        # زر طباعة محسن
        btn_prn = QtWidgets.QPushButton('🖨️ طباعة')
        btn_prn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6f42c1, stop:1 #59359a);
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8662d0, stop:1 #6f42c1);
            }
        """)
        btn_prn.clicked.connect(lambda: self.print_widget_advanced(widget, title))
        h.addWidget(btn_prn)

        if isinstance(parent, QtWidgets.QTabWidget):
            wrapper = QtWidgets.QWidget()
            wrapper_layout = QtWidgets.QVBoxLayout(wrapper)
            wrapper_layout.addWidget(widget)
            wrapper_layout.addLayout(h)
            parent.addTab(wrapper, title)
        else:
            parent.addLayout(h)

    def save_pdf_advanced(self, widget, title):
        """حفظ PDF محسن مع خيارات متقدمة"""
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f'{title}_{timestamp}.pdf'

        path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self, '💾 حفظ تقرير PDF', default_name, 'PDF Files (*.pdf)'
        )
        if not path:
            return

        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(path)

            # إصلاح مشكلة حجم الصفحة
            try:
                printer.setPageSize(QtGui.QPageSize(QtGui.QPageSize.A4))
            except:
                # للإصدارات الأقدم
                printer.setPageSize(QPrinter.A4)

            try:
                printer.setPageOrientation(QtGui.QPageLayout.Portrait)
            except:
                # للإصدارات الأقدم
                printer.setOrientation(QPrinter.Portrait)

            printer.setFullPage(False)

            # تعيين الهوامش
            try:
                printer.setPageMargins(QtCore.QMarginsF(20, 20, 20, 20), QtGui.QPageLayout.Millimeter)
            except:
                # للإصدارات الأقدم
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء painter
            painter = QtGui.QPainter()
            if not painter.begin(printer):
                QtWidgets.QMessageBox.critical(
                    self, '❌ خطأ', 'فشل في إنشاء ملف PDF'
                )
                return

            try:
                # الحصول على أبعاد الصفحة
                try:
                    page_rect = printer.pageRect(QPrinter.DevicePixel)
                except:
                    # للإصدارات الأقدم
                    page_rect = printer.pageRect()

                # إضافة رأس الصفحة
                header_height = self.draw_pdf_header(painter, title, page_rect)

                # حساب منطقة المحتوى
                content_y = header_height + 20
                footer_height = 80
                content_height = page_rect.height() - content_y - footer_height

                # إنشاء صورة من الـ widget
                widget_size = widget.size()
                pixmap = QtGui.QPixmap(widget_size)
                pixmap.fill(QtCore.Qt.white)

                # رسم الـ widget على الـ pixmap
                widget_painter = QtGui.QPainter(pixmap)
                widget.render(widget_painter, QtCore.QPoint(0, 0))
                widget_painter.end()

                # حساب التكبير المناسب
                content_width = page_rect.width() - 100
                scale_x = content_width / widget_size.width()
                scale_y = content_height / widget_size.height()
                scale = min(scale_x, scale_y, 1.0)  # لا نكبر أكثر من الحجم الأصلي

                # حساب الموقع المركزي
                scaled_width = int(widget_size.width() * scale)
                scaled_height = int(widget_size.height() * scale)
                x = (page_rect.width() - scaled_width) // 2
                y = content_y

                # رسم الصورة
                target_rect = QtCore.QRect(x, y, scaled_width, scaled_height)
                painter.drawPixmap(target_rect, pixmap)

                # إضافة تذييل الصفحة
                self.draw_pdf_footer(painter, page_rect)

            finally:
                painter.end()

            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح', f'تم حفظ التقرير بصيغة PDF:\n{path}'
            )

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في حفظ ملف PDF:\n{str(e)}'
            )

    def draw_pdf_header(self, painter, title, page_rect):
        """رسم رأس الصفحة في PDF مع بيانات المدرسة"""
        # تحميل إعدادات المدرسة
        school_settings = self.load_school_settings()

        y_pos = 20

        # رسم بيانات المدرسة
        school_font = QtGui.QFont("Arial", 12, QtGui.QFont.Bold)
        painter.setFont(school_font)
        painter.setPen(QtCore.Qt.black)

        # الإدارة العامة
        if school_settings['education_department']:
            dept_rect = QtCore.QRect(0, y_pos, page_rect.width(), 25)
            painter.drawText(dept_rect, QtCore.Qt.AlignCenter, school_settings['education_department'])
            y_pos += 30

        # مكتب التعليم
        if school_settings['education_office']:
            office_rect = QtCore.QRect(0, y_pos, page_rect.width(), 25)
            painter.drawText(office_rect, QtCore.Qt.AlignCenter, school_settings['education_office'])
            y_pos += 30

        # اسم المدرسة
        if school_settings['school_name']:
            school_rect = QtCore.QRect(0, y_pos, page_rect.width(), 25)
            painter.drawText(school_rect, QtCore.Qt.AlignCenter, school_settings['school_name'])
            y_pos += 40

        # خط العنوان
        title_font = QtGui.QFont("Arial", 16, QtGui.QFont.Bold)
        painter.setFont(title_font)
        painter.setPen(QtCore.Qt.black)

        # رسم العنوان
        title_rect = QtCore.QRect(0, y_pos, page_rect.width(), 30)
        painter.drawText(title_rect, QtCore.Qt.AlignCenter, f"📋 {title}")
        y_pos += 40

        # رسم خط فاصل
        painter.setPen(QtGui.QPen(QtCore.Qt.black, 2))
        painter.drawLine(50, y_pos, page_rect.width() - 50, y_pos)
        y_pos += 20

        # معلومات إضافية
        info_font = QtGui.QFont("Arial", 10)
        painter.setFont(info_font)
        painter.setPen(QtCore.Qt.black)

        current_date = datetime.datetime.now().strftime('%Y/%m/%d %H:%M')
        hijri_date = self.lbl_hijri.text() if hasattr(self, 'lbl_hijri') else 'غير متوفر'

        info_text = f"تاريخ الإنشاء: {current_date}"
        if hijri_date and hijri_date != 'غير متوفر':
            info_text += f" | التاريخ الهجري: {hijri_date}"

        info_rect = QtCore.QRect(0, y_pos, page_rect.width(), 25)
        painter.drawText(info_rect, QtCore.Qt.AlignCenter, info_text)
        y_pos += 30

        return y_pos  # إرجاع ارتفاع الرأس

    def draw_pdf_footer(self, painter, page_rect):
        """رسم تذييل الصفحة في PDF"""
        footer_font = QtGui.QFont("Arial", 8)
        painter.setFont(footer_font)
        painter.setPen(QtCore.Qt.black)

        # رسم خط فاصل
        y_pos = page_rect.height() - 60
        painter.setPen(QtGui.QPen(QtCore.Qt.black, 1))
        painter.drawLine(50, y_pos, page_rect.width() - 50, y_pos)

        # نص التذييل
        footer_text = "نظام إدارة الغياب المدرسي - تم إنشاؤه تلقائياً"
        footer_rect = QtCore.QRect(0, y_pos + 10, page_rect.width(), 30)
        painter.drawText(footer_rect, QtCore.Qt.AlignCenter, footer_text)

    def save_pdf(self, widget, default_name):
        """الدالة القديمة للتوافق مع الكود الموجود"""
        self.save_pdf_advanced(widget, default_name.replace('.pdf', ''))

    def save_word_advanced(self, widget, title):
        """حفظ Word محسن مع تنسيق أفضل"""
        if not DOCX_AVAILABLE:
            QtWidgets.QMessageBox.warning(
                self, '⚠️ تحذير',
                'مكتبة python-docx غير متوفرة.\nيرجى تثبيتها باستخدام: pip install python-docx'
            )
            return

        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f'{title}_{timestamp}.docx'

        path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self, '📝 حفظ تقرير Word', default_name, 'Word Files (*.docx)'
        )
        if not path:
            return

        try:
            doc = Document()

            # إضافة عنوان المستند
            title_paragraph = doc.add_heading(f'🎓 {title}', 0)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # إضافة معلومات التاريخ
            current_date = datetime.datetime.now().strftime('%Y/%m/%d %H:%M')
            hijri_date = self.lbl_hijri.text()

            date_paragraph = doc.add_paragraph()
            date_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            date_paragraph.add_run(f'تاريخ الإنشاء: {current_date}\n')
            date_paragraph.add_run(f'التاريخ الهجري: {hijri_date}')

            # إضافة فاصل
            doc.add_paragraph('─' * 50).alignment = WD_ALIGN_PARAGRAPH.CENTER

            # إضافة الجدول
            rows = widget.rowCount()
            cols = widget.columnCount()

            if rows > 0 and cols > 0:
                table = doc.add_table(rows=rows+1, cols=cols)
                table.style = 'Table Grid'

                # تنسيق رأس الجدول
                hdr_cells = table.rows[0].cells
                for j in range(cols):
                    header_item = widget.horizontalHeaderItem(j)
                    if header_item:
                        hdr_cells[j].text = header_item.text()
                        # تنسيق خلية الرأس
                        hdr_cells[j].paragraphs[0].runs[0].bold = True

                # إضافة بيانات الجدول
                for i in range(rows):
                    row_cells = table.rows[i+1].cells
                    for j in range(cols):
                        item = widget.item(i, j)
                        if item:
                            row_cells[j].text = item.text()

            # إضافة تذييل
            footer_paragraph = doc.add_paragraph()
            footer_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            footer_paragraph.add_run('\n─' * 50 + '\n')
            footer_paragraph.add_run('نظام إدارة الغياب المدرسي - تم إنشاؤه تلقائياً')

            doc.save(path)

            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح', f'تم حفظ التقرير بصيغة Word:\n{path}'
            )

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في حفظ ملف Word:\n{str(e)}'
            )

    def save_excel_advanced(self, widget, title):
        """حفظ Excel محسن مع تنسيق أفضل"""
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        default_name = f'{title}_{timestamp}.xlsx'

        path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self, '📊 حفظ تقرير Excel', default_name, 'Excel Files (*.xlsx)'
        )
        if not path:
            return

        try:
            # إنشاء DataFrame من بيانات الجدول
            rows = widget.rowCount()
            cols = widget.columnCount()

            if rows == 0 or cols == 0:
                QtWidgets.QMessageBox.warning(
                    self, '⚠️ تحذير', 'لا توجد بيانات للحفظ'
                )
                return

            # جمع أسماء الأعمدة
            headers = []
            for j in range(cols):
                header_item = widget.horizontalHeaderItem(j)
                headers.append(header_item.text() if header_item else f'عمود {j+1}')

            # جمع البيانات
            data = []
            for i in range(rows):
                row_data = []
                for j in range(cols):
                    item = widget.item(i, j)
                    row_data.append(item.text() if item else '')
                data.append(row_data)

            # إنشاء DataFrame
            df = pd.DataFrame(data, columns=headers)

            # حفظ الملف مع تنسيق
            with pd.ExcelWriter(path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='التقرير', index=False)

                # الحصول على workbook و worksheet للتنسيق
                workbook = writer.book
                worksheet = writer.sheets['التقرير']

                # تنسيق الرأس
                from openpyxl.styles import Font, PatternFill, Alignment

                header_font = Font(bold=True, color='FFFFFF')
                header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
                center_alignment = Alignment(horizontal='center', vertical='center')

                for col_num, column in enumerate(df.columns, 1):
                    cell = worksheet.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment

                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

                # إضافة معلومات إضافية في ورقة منفصلة
                info_sheet = workbook.create_sheet('معلومات التقرير')
                info_data = [
                    ['اسم التقرير', title],
                    ['تاريخ الإنشاء', datetime.datetime.now().strftime('%Y/%m/%d %H:%M')],
                    ['التاريخ الهجري', self.lbl_hijri.text()],
                    ['عدد السجلات', len(df)],
                    ['النظام', 'نظام إدارة الغياب المدرسي']
                ]

                for row_num, (key, value) in enumerate(info_data, 1):
                    info_sheet.cell(row=row_num, column=1, value=key).font = Font(bold=True)
                    info_sheet.cell(row=row_num, column=2, value=value)

                # تعديل عرض أعمدة ورقة المعلومات
                info_sheet.column_dimensions['A'].width = 20
                info_sheet.column_dimensions['B'].width = 30

            QtWidgets.QMessageBox.information(
                self, '✅ تم بنجاح', f'تم حفظ التقرير بصيغة Excel:\n{path}'
            )

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في حفظ ملف Excel:\n{str(e)}'
            )

    def save_word(self, widget, default_name):
        """الدالة القديمة للتوافق مع الكود الموجود"""
        self.save_word_advanced(widget, default_name.replace('.docx', ''))

    def print_widget_advanced(self, widget, title):
        """طباعة محسنة مع معاينة وخيارات متقدمة"""
        try:
            # إنشاء نافذة إعدادات الطباعة
            print_dialog = PrintSettingsDialog(self, title)
            if print_dialog.exec() != QtWidgets.QDialog.Accepted:
                return

            settings = print_dialog.get_settings()

            # إعداد الطابعة
            printer = QPrinter(QPrinter.HighResolution)

            # تعيين حجم الصفحة
            try:
                printer.setPageSize(QtGui.QPageSize(QtGui.QPageSize.A4))
            except:
                printer.setPageSize(QPrinter.A4)

            # تعيين اتجاه الصفحة
            try:
                orientation = QtGui.QPageLayout.Landscape if settings['orientation'] == 'landscape' else QtGui.QPageLayout.Portrait
                printer.setPageOrientation(orientation)
            except:
                orientation = QPrinter.Landscape if settings['orientation'] == 'landscape' else QPrinter.Portrait
                printer.setPageOrientation(orientation)

            printer.setFullPage(False)

            # تعيين الهوامش
            margins = settings['margins']
            try:
                printer.setPageMargins(
                    QtCore.QMarginsF(margins['left'], margins['top'], margins['right'], margins['bottom']),
                    QtGui.QPageLayout.Millimeter
                )
            except:
                printer.setPageMargins(margins['top'], margins['right'],
                                     margins['bottom'], margins['left'], QPrinter.Millimeter)

            # معاينة الطباعة
            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.setWindowTitle(f'🖨️ معاينة طباعة - {title}')
            preview_dialog.paintRequested.connect(
                lambda pr: self.paint_for_print(pr, widget, title, settings)
            )
            preview_dialog.exec()

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, '❌ خطأ', f'فشل في الطباعة:\n{str(e)}'
            )

    def paint_for_print(self, printer, widget, title, settings):
        """رسم المحتوى للطباعة مع التنسيق المحسن"""
        painter = QtGui.QPainter(printer)

        try:
            # الحصول على أبعاد الصفحة
            try:
                page_rect = printer.pageRect(QPrinter.DevicePixel)
            except:
                page_rect = printer.pageRect()

            # رسم الرأس إذا كان مطلوباً
            if settings['include_header']:
                self.draw_print_header(painter, title, page_rect, settings)

            # حساب منطقة المحتوى
            content_y = 150 if settings['include_header'] else 50
            footer_height = 80 if settings['include_footer'] else 20

            content_rect = QtCore.QRect(
                50, content_y,
                page_rect.width() - 100,
                page_rect.height() - content_y - footer_height
            )

            # إنشاء صورة من الـ widget
            widget_size = widget.size()
            pixmap = QtGui.QPixmap(widget_size)
            pixmap.fill(QtCore.Qt.white)

            # رسم الـ widget على الـ pixmap
            widget_painter = QtGui.QPainter(pixmap)
            widget.render(widget_painter, QtCore.QPoint(0, 0))
            widget_painter.end()

            # حساب التكبير المناسب
            scale_x = content_rect.width() / widget_size.width()
            scale_y = content_rect.height() / widget_size.height()
            scale = min(scale_x, scale_y, 1.0) * (settings['scale'] / 100.0)

            # حساب الموقع المركزي
            scaled_width = int(widget_size.width() * scale)
            scaled_height = int(widget_size.height() * scale)
            x = content_rect.x() + (content_rect.width() - scaled_width) // 2
            y = content_rect.y()

            # رسم الصورة
            target_rect = QtCore.QRect(x, y, scaled_width, scaled_height)
            painter.drawPixmap(target_rect, pixmap)

            # رسم التذييل إذا كان مطلوباً
            if settings['include_footer']:
                self.draw_print_footer(painter, page_rect, settings)

        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
        finally:
            if painter.isActive():
                painter.end()

    def draw_print_header(self, painter, title, page_rect, settings):
        """رسم رأس الطباعة مع بيانات المدرسة"""
        # تحميل إعدادات المدرسة
        school_settings = self.load_school_settings()

        y_pos = 20

        # رسم بيانات المدرسة
        school_font = QtGui.QFont("Arial", 11, QtGui.QFont.Bold)
        painter.setFont(school_font)
        painter.setPen(QtCore.Qt.black)

        # الإدارة العامة
        if school_settings['education_department']:
            dept_rect = QtCore.QRect(50, y_pos, page_rect.width() - 100, 20)
            painter.drawText(dept_rect, QtCore.Qt.AlignCenter, school_settings['education_department'])
            y_pos += 25

        # مكتب التعليم
        if school_settings['education_office']:
            office_rect = QtCore.QRect(50, y_pos, page_rect.width() - 100, 20)
            painter.drawText(office_rect, QtCore.Qt.AlignCenter, school_settings['education_office'])
            y_pos += 25

        # اسم المدرسة
        if school_settings['school_name']:
            school_rect = QtCore.QRect(50, y_pos, page_rect.width() - 100, 20)
            painter.drawText(school_rect, QtCore.Qt.AlignCenter, school_settings['school_name'])
            y_pos += 30

        # خط العنوان
        title_font = QtGui.QFont("Arial", 14, QtGui.QFont.Bold)
        painter.setFont(title_font)
        painter.setPen(QtCore.Qt.black)

        # رسم العنوان
        title_rect = QtCore.QRect(50, y_pos, page_rect.width() - 100, 25)
        painter.drawText(title_rect, QtCore.Qt.AlignCenter, f"📋 {title}")
        y_pos += 35

        # رسم خط فاصل
        painter.setPen(QtGui.QPen(QtCore.Qt.black, 2))
        painter.drawLine(50, y_pos, page_rect.width() - 50, y_pos)
        y_pos += 15

        # معلومات إضافية
        if settings['include_date']:
            info_font = QtGui.QFont("Arial", 9)
            painter.setFont(info_font)
            painter.setPen(QtCore.Qt.black)

            current_date = datetime.datetime.now().strftime('%Y/%m/%d %H:%M')
            hijri_date = self.lbl_hijri.text() if hasattr(self, 'lbl_hijri') else 'غير متوفر'

            info_text = f"تاريخ الطباعة: {current_date}"
            if hijri_date and hijri_date != 'غير متوفر':
                info_text += f" | التاريخ الهجري: {hijri_date}"

            info_rect = QtCore.QRect(50, y_pos, page_rect.width() - 100, 20)
            painter.drawText(info_rect, QtCore.Qt.AlignCenter, info_text)

    def draw_print_footer(self, painter, page_rect, settings):
        """رسم تذييل الطباعة"""
        footer_font = QtGui.QFont("Arial", 8)
        painter.setFont(footer_font)
        painter.setPen(QtCore.Qt.black)

        # رسم خط فاصل
        y_pos = page_rect.height() - 60
        painter.drawLine(50, y_pos, page_rect.width() - 50, y_pos)

        # نص التذييل
        footer_text = "نظام إدارة الغياب المدرسي"
        if settings['include_page_numbers']:
            footer_text += " - صفحة 1"

        footer_rect = QtCore.QRect(50, y_pos + 10, page_rect.width() - 100, 30)
        painter.drawText(footer_rect, QtCore.Qt.AlignCenter, footer_text)

    def print_widget(self, widget):
        """الدالة القديمة للتوافق مع الكود الموجود"""
        self.print_widget_advanced(widget, "تقرير")

    def show_about(self):
        """عرض نافذة حول البرنامج"""
        about_dialog = QtWidgets.QDialog(self)
        about_dialog.setWindowTitle("حول البرنامج")
        about_dialog.setFixedSize(500, 600)
        about_dialog.setLayoutDirection(QtCore.Qt.RightToLeft)

        layout = QtWidgets.QVBoxLayout(about_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # شعار البرنامج
        logo_label = QtWidgets.QLabel("🎓")
        logo_label.setAlignment(QtCore.Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 64px; margin: 20px;")
        layout.addWidget(logo_label)

        # اسم البرنامج
        title_label = QtWidgets.QLabel("نظام إدارة الغياب المدرسي")
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px;
        """)
        layout.addWidget(title_label)

        # الإصدار
        version_label = QtWidgets.QLabel("الإصدار 2.0 - التصميم المطور")
        version_label.setAlignment(QtCore.Qt.AlignCenter)
        version_label.setStyleSheet("""
            font-size: 14px;
            color: #7f8c8d;
            margin: 5px;
        """)
        layout.addWidget(version_label)

        # خط فاصل
        line = QtWidgets.QFrame()
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Sunken)
        layout.addWidget(line)

        # معلومات المطور
        dev_info = QtWidgets.QLabel("""
        <div style='text-align: center;'>
        <h3>👨‍💻 معلومات المطور</h3>
        <p><b>المطور:</b> Ahmed Rabie</p>
        <p><b>الواتساب:</b> 00201154013932</p>
        <p><b>البريد الإلكتروني:</b> <EMAIL></p>
        <p><b>التخصص:</b> تطوير أنظمة إدارة المدارس</p>
        </div>
        """)
        dev_info.setStyleSheet("""
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        """)
        layout.addWidget(dev_info)

        # ميزات البرنامج
        features_info = QtWidgets.QLabel("""
        <div style='text-align: center;'>
        <h3>✨ ميزات البرنامج</h3>
        <ul style='text-align: right;'>
        <li>إدارة شاملة لغياب الطلاب</li>
        <li>تقارير متقدمة مع إحصائيات دقيقة</li>
        <li>حفظ بصيغ متعددة (PDF, Word, Excel)</li>
        <li>طباعة احترافية مع معاينة</li>
        <li>واجهة مستخدم عصرية وسهلة</li>
        <li>دعم التاريخ الهجري والميلادي</li>
        </ul>
        </div>
        """)
        features_info.setStyleSheet("""
            background-color: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        """)
        layout.addWidget(features_info)

        # أزرار التواصل
        contact_layout = QtWidgets.QHBoxLayout()

        whatsapp_btn = QtWidgets.QPushButton("📱 واتساب")
        whatsapp_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #25d366, stop:1 #128c7e);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34e79a, stop:1 #25d366);
            }
        """)
        whatsapp_btn.clicked.connect(lambda: self.open_whatsapp())

        email_btn = QtWidgets.QPushButton("📧 إيميل")
        email_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
        """)
        email_btn.clicked.connect(lambda: self.open_email())

        contact_layout.addWidget(whatsapp_btn)
        contact_layout.addWidget(email_btn)
        layout.addLayout(contact_layout)

        # زر إغلاق
        close_btn = QtWidgets.QPushButton("إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 30px;
                font-weight: bold;
                font-size: 14px;
                margin-top: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ec7063, stop:1 #e74c3c);
            }
        """)
        close_btn.clicked.connect(about_dialog.accept)
        layout.addWidget(close_btn)

        about_dialog.exec()

    def open_whatsapp(self):
        """فتح واتساب"""
        import webbrowser
        webbrowser.open("https://wa.me/00201154013932")

    def open_email(self):
        """فتح البريد الإلكتروني"""
        import webbrowser
        webbrowser.open("mailto:<EMAIL>")

    def closeEvent(self, event):
        encrypt_and_cleanup()
        event.accept()


class StatisticsDialog(QtWidgets.QDialog):
    """نافذة الإحصائيات المرئية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('📊 الإحصائيات المرئية')
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.resize(1000, 700)
        self.setup_ui()
        self.load_statistics()

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)

        # شريط العنوان
        title_label = QtWidgets.QLabel('📊 إحصائيات الغياب المدرسي')
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #74b9ff, stop:1 #0984e3);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # تبويبات الإحصائيات
        tabs = QtWidgets.QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: #007bff;
                color: white;
            }
        """)

        # تبويب الإحصائيات العامة
        general_tab = QtWidgets.QWidget()
        general_layout = QtWidgets.QVBoxLayout(general_tab)

        # إحصائيات سريعة
        stats_frame = QtWidgets.QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }
        """)
        stats_layout = QtWidgets.QHBoxLayout(stats_frame)

        # بطاقات الإحصائيات
        self.create_stat_card(stats_layout, "👥 إجمالي الطلاب", "0", "#28a745")
        self.create_stat_card(stats_layout, "📅 أيام الدراسة", "0", "#17a2b8")
        self.create_stat_card(stats_layout, "❌ إجمالي الغياب", "0", "#dc3545")
        self.create_stat_card(stats_layout, "📊 معدل الغياب", "0%", "#ffc107")

        general_layout.addWidget(stats_frame)

        # جدول أعلى الطلاب غياباً
        top_absent_label = QtWidgets.QLabel("🔝 أعلى الطلاب غياباً")
        top_absent_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px 0;")
        general_layout.addWidget(top_absent_label)

        self.top_absent_table = QtWidgets.QTableWidget()
        self.top_absent_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #dee2e6;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            QHeaderView::section {
                background-color: #6c757d;
                color: white;
                padding: 8px;
                font-weight: bold;
                border: none;
            }
        """)
        general_layout.addWidget(self.top_absent_table)

        tabs.addTab(general_tab, "📊 إحصائيات عامة")

        # تبويب الرسوم البيانية
        charts_tab = QtWidgets.QWidget()
        charts_layout = QtWidgets.QVBoxLayout(charts_tab)

        # إضافة رسم بياني للغياب حسب الفصول
        chart_label = QtWidgets.QLabel("📈 توزيع الغياب حسب الفصول")
        chart_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px 0;")
        charts_layout.addWidget(chart_label)

        # مساحة للرسم البياني
        chart_frame = QtWidgets.QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                min-height: 400px;
            }
        """)
        chart_layout = QtWidgets.QVBoxLayout(chart_frame)

        # رسالة مؤقتة للرسم البياني
        chart_placeholder = QtWidgets.QLabel("📊 الرسوم البيانية ستظهر هنا")
        chart_placeholder.setAlignment(QtCore.Qt.AlignCenter)
        chart_placeholder.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #6c757d;
                background-color: #f8f9fa;
                border: 2px dashed #dee2e6;
                border-radius: 8px;
                padding: 50px;
            }
        """)
        chart_layout.addWidget(chart_placeholder)

        charts_layout.addWidget(chart_frame)
        tabs.addTab(charts_tab, "📈 رسوم بيانية")

        layout.addWidget(tabs)

        # أزرار الإجراءات
        buttons_layout = QtWidgets.QHBoxLayout()

        refresh_btn = QtWidgets.QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_statistics)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        buttons_layout.addWidget(refresh_btn)

        export_btn = QtWidgets.QPushButton("📤 تصدير")
        export_btn.clicked.connect(self.export_statistics)
        export_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        buttons_layout.addWidget(export_btn)

        buttons_layout.addStretch()

        close_btn = QtWidgets.QPushButton("❌ إغلاق")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

    def create_stat_card(self, layout, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QtWidgets.QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                min-width: 150px;
            }}
        """)

        card_layout = QtWidgets.QVBoxLayout(card)

        title_label = QtWidgets.QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_label.setAlignment(QtCore.Qt.AlignCenter)

        value_label = QtWidgets.QLabel(value)
        value_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
            }
        """)
        value_label.setAlignment(QtCore.Qt.AlignCenter)
        value_label.setObjectName(f"value_{title}")  # لتحديث القيمة لاحقاً

        card_layout.addWidget(title_label)
        card_layout.addWidget(value_label)

        layout.addWidget(card)

    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            '#28a745': '#1e7e34',
            '#17a2b8': '#117a8b',
            '#ffc107': '#e0a800',
            '#dc3545': '#c82333'
        }
        return color_map.get(color, color)

    def load_statistics(self):
        """تحميل الإحصائيات من قاعدة البيانات"""
        try:
            conn = get_connection()
            cur = conn.cursor()

            # إجمالي الطلاب
            cur.execute("SELECT COUNT(*) FROM students")
            total_students = cur.fetchone()[0]

            # أيام الدراسة
            cur.execute("SELECT COUNT(DISTINCT date) FROM attendance")
            study_days = cur.fetchone()[0]

            # إجمالي الغياب
            cur.execute("SELECT COUNT(*) FROM attendance WHERE status='absent'")
            total_absences = cur.fetchone()[0]

            # معدل الغياب
            cur.execute("SELECT COUNT(*) FROM attendance")
            total_records = cur.fetchone()[0]
            absence_rate = (total_absences / total_records * 100) if total_records > 0 else 0

            # تحديث البطاقات
            self.update_stat_card("👥 إجمالي الطلاب", str(total_students))
            self.update_stat_card("📅 أيام الدراسة", str(study_days))
            self.update_stat_card("❌ إجمالي الغياب", str(total_absences))
            self.update_stat_card("📊 معدل الغياب", f"{absence_rate:.1f}%")

            # تحميل جدول أعلى الطلاب غياباً
            cur.execute("""
                SELECT s.name, s.grade, s.section,
                       COUNT(a.id) as total_days,
                       SUM(CASE WHEN a.status='absent' THEN 1 ELSE 0 END) as absent_days,
                       ROUND(SUM(CASE WHEN a.status='absent' THEN 1 ELSE 0 END) * 100.0 / COUNT(a.id), 2) as absence_percentage
                FROM students s
                LEFT JOIN attendance a ON s.id = a.student_id
                GROUP BY s.id, s.name, s.grade, s.section
                HAVING COUNT(a.id) > 0
                ORDER BY absence_percentage DESC, absent_days DESC
                LIMIT 10
            """)

            top_absent_data = cur.fetchall()

            # إعداد الجدول
            self.top_absent_table.setRowCount(len(top_absent_data))
            self.top_absent_table.setColumnCount(6)
            self.top_absent_table.setHorizontalHeaderLabels([
                "الترتيب", "اسم الطالب", "المرحلة", "الفصل", "أيام الغياب", "نسبة الغياب"
            ])

            for i, (name, grade, section, total_days, absent_days, percentage) in enumerate(top_absent_data):
                self.top_absent_table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(i + 1)))
                self.top_absent_table.setItem(i, 1, QtWidgets.QTableWidgetItem(name))
                self.top_absent_table.setItem(i, 2, QtWidgets.QTableWidgetItem(grade or ""))
                self.top_absent_table.setItem(i, 3, QtWidgets.QTableWidgetItem(section or ""))
                self.top_absent_table.setItem(i, 4, QtWidgets.QTableWidgetItem(str(absent_days or 0)))

                percentage_item = QtWidgets.QTableWidgetItem(f"{percentage or 0:.1f}%")
                if percentage and percentage > 20:
                    percentage_item.setBackground(QtGui.QColor('#ffebee'))
                elif percentage and percentage > 10:
                    percentage_item.setBackground(QtGui.QColor('#fff3e0'))
                else:
                    percentage_item.setBackground(QtGui.QColor('#e8f5e8'))

                self.top_absent_table.setItem(i, 5, percentage_item)

            self.top_absent_table.resizeColumnsToContents()
            conn.close()

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, "❌ خطأ", f"فشل في تحميل الإحصائيات:\n{str(e)}"
            )

    def update_stat_card(self, title, value):
        """تحديث قيمة بطاقة إحصائية"""
        value_label = self.findChild(QtWidgets.QLabel, f"value_{title}")
        if value_label:
            value_label.setText(value)

    def export_statistics(self):
        """تصدير الإحصائيات"""
        try:
            file_path, _ = QtWidgets.QFileDialog.getSaveFileName(
                self, "حفظ الإحصائيات", "statistics_report.xlsx",
                "Excel Files (*.xlsx);;PDF Files (*.pdf)"
            )

            if file_path:
                if file_path.endswith('.xlsx'):
                    self.export_to_excel(file_path)
                elif file_path.endswith('.pdf'):
                    self.export_to_pdf(file_path)

                QtWidgets.QMessageBox.information(
                    self, "✅ تم بنجاح", f"تم تصدير الإحصائيات إلى:\n{file_path}"
                )
        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, "❌ خطأ", f"فشل في تصدير الإحصائيات:\n{str(e)}"
            )

    def export_to_excel(self, file_path):
        """تصدير إلى Excel"""
        # سيتم تنفيذها لاحقاً
        pass

    def export_to_pdf(self, file_path):
        """تصدير إلى PDF"""
        # سيتم تنفيذها لاحقاً
        pass


class PrintSettingsDialog(QtWidgets.QDialog):
    """نافذة إعدادات الطباعة"""

    def __init__(self, parent=None, title=""):
        super().__init__(parent)
        self.setWindowTitle('🖨️ إعدادات الطباعة')
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.resize(400, 500)
        self.title = title
        self.setup_ui()

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)

        # عنوان النافذة
        title_label = QtWidgets.QLabel(f'إعدادات طباعة: {self.title}')
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # إعدادات الصفحة
        page_group = QtWidgets.QGroupBox('📄 إعدادات الصفحة')
        page_layout = QtWidgets.QFormLayout(page_group)

        # اتجاه الصفحة
        self.orientation_combo = QtWidgets.QComboBox()
        self.orientation_combo.addItems(['عمودي', 'أفقي'])
        page_layout.addRow('🔄 اتجاه الصفحة:', self.orientation_combo)

        # التكبير/التصغير
        self.scale_spin = QtWidgets.QSpinBox()
        self.scale_spin.setRange(25, 200)
        self.scale_spin.setValue(100)
        self.scale_spin.setSuffix('%')
        page_layout.addRow('🔍 التكبير:', self.scale_spin)

        layout.addWidget(page_group)

        # إعدادات الهوامش
        margins_group = QtWidgets.QGroupBox('📏 الهوامش (مم)')
        margins_layout = QtWidgets.QGridLayout(margins_group)

        self.margin_top = QtWidgets.QSpinBox()
        self.margin_top.setRange(0, 50)
        self.margin_top.setValue(20)
        margins_layout.addWidget(QtWidgets.QLabel('⬆️ أعلى:'), 0, 0)
        margins_layout.addWidget(self.margin_top, 0, 1)

        self.margin_bottom = QtWidgets.QSpinBox()
        self.margin_bottom.setRange(0, 50)
        self.margin_bottom.setValue(20)
        margins_layout.addWidget(QtWidgets.QLabel('⬇️ أسفل:'), 0, 2)
        margins_layout.addWidget(self.margin_bottom, 0, 3)

        self.margin_left = QtWidgets.QSpinBox()
        self.margin_left.setRange(0, 50)
        self.margin_left.setValue(20)
        margins_layout.addWidget(QtWidgets.QLabel('⬅️ يسار:'), 1, 0)
        margins_layout.addWidget(self.margin_left, 1, 1)

        self.margin_right = QtWidgets.QSpinBox()
        self.margin_right.setRange(0, 50)
        self.margin_right.setValue(20)
        margins_layout.addWidget(QtWidgets.QLabel('➡️ يمين:'), 1, 2)
        margins_layout.addWidget(self.margin_right, 1, 3)

        layout.addWidget(margins_group)

        # إعدادات المحتوى
        content_group = QtWidgets.QGroupBox('📝 إعدادات المحتوى')
        content_layout = QtWidgets.QVBoxLayout(content_group)

        self.include_header = QtWidgets.QCheckBox('📋 تضمين رأس الصفحة')
        self.include_header.setChecked(True)
        content_layout.addWidget(self.include_header)

        self.include_footer = QtWidgets.QCheckBox('📄 تضمين تذييل الصفحة')
        self.include_footer.setChecked(True)
        content_layout.addWidget(self.include_footer)

        self.include_date = QtWidgets.QCheckBox('📅 تضمين التاريخ')
        self.include_date.setChecked(True)
        content_layout.addWidget(self.include_date)

        self.include_page_numbers = QtWidgets.QCheckBox('🔢 تضمين أرقام الصفحات')
        self.include_page_numbers.setChecked(True)
        content_layout.addWidget(self.include_page_numbers)

        layout.addWidget(content_group)

        # أزرار الإجراءات
        buttons_layout = QtWidgets.QHBoxLayout()

        # زر المعاينة
        preview_btn = QtWidgets.QPushButton('👁️ معاينة')
        preview_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        preview_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(preview_btn)

        # زر الإلغاء
        cancel_btn = QtWidgets.QPushButton('❌ إلغاء')
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def get_settings(self):
        """الحصول على إعدادات الطباعة"""
        return {
            'orientation': 'landscape' if self.orientation_combo.currentIndex() == 1 else 'portrait',
            'scale': self.scale_spin.value(),
            'margins': {
                'top': self.margin_top.value(),
                'bottom': self.margin_bottom.value(),
                'left': self.margin_left.value(),
                'right': self.margin_right.value()
            },
            'include_header': self.include_header.isChecked(),
            'include_footer': self.include_footer.isChecked(),
            'include_date': self.include_date.isChecked(),
            'include_page_numbers': self.include_page_numbers.isChecked()
        }


class SchoolSettingsDialog(QtWidgets.QDialog):
    """نافذة إعدادات المدرسة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('⚙️ إعدادات المدرسة')
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.resize(500, 400)
        self.setup_ui()
        self.load_current_settings()

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)

        # عنوان النافذة
        title_label = QtWidgets.QLabel('⚙️ إعدادات بيانات المدرسة')
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #74b9ff, stop:1 #0984e3);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # نموذج الإدخال
        form_layout = QtWidgets.QFormLayout()

        # اسم المدرسة
        self.school_name_edit = QtWidgets.QLineEdit()
        self.school_name_edit.setPlaceholderText('مثال: متوسطة الملك عبدالعزيز')
        self.school_name_edit.setStyleSheet(self.get_input_style())
        form_layout.addRow('🏫 اسم المدرسة:', self.school_name_edit)

        # الإدارة العامة
        self.education_dept_edit = QtWidgets.QLineEdit()
        self.education_dept_edit.setPlaceholderText('مثال: الإدارة العامة للتعليم بمنطقة الرياض')
        self.education_dept_edit.setStyleSheet(self.get_input_style())
        form_layout.addRow('🏛️ الإدارة العامة:', self.education_dept_edit)

        # مكتب التعليم
        self.education_office_edit = QtWidgets.QLineEdit()
        self.education_office_edit.setPlaceholderText('مثال: مكتب التعليم بشرق الرياض')
        self.education_office_edit.setStyleSheet(self.get_input_style())
        form_layout.addRow('🏢 مكتب التعليم:', self.education_office_edit)

        # شعار الوزارة
        logo_layout = QtWidgets.QHBoxLayout()
        self.logo_path_edit = QtWidgets.QLineEdit()
        self.logo_path_edit.setPlaceholderText('اختر ملف الشعار (PNG, JPG)')
        self.logo_path_edit.setStyleSheet(self.get_input_style())

        browse_btn = QtWidgets.QPushButton('📁 تصفح')
        browse_btn.clicked.connect(self.browse_logo)
        browse_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)

        logo_layout.addWidget(self.logo_path_edit)
        logo_layout.addWidget(browse_btn)
        form_layout.addRow('🖼️ شعار الوزارة:', logo_layout)

        layout.addLayout(form_layout)

        # معاينة
        preview_label = QtWidgets.QLabel('📋 معاينة الرأس:')
        preview_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(preview_label)

        self.preview_area = QtWidgets.QLabel()
        self.preview_area.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px dashed #dee2e6;
                border-radius: 8px;
                padding: 20px;
                min-height: 80px;
                font-size: 12px;
                color: #6c757d;
            }
        """)
        self.preview_area.setAlignment(QtCore.Qt.AlignCenter)
        self.preview_area.setText('ستظهر معاينة الرأس هنا عند ملء البيانات')
        layout.addWidget(self.preview_area)

        # ربط التحديث التلقائي للمعاينة
        self.school_name_edit.textChanged.connect(self.update_preview)
        self.education_dept_edit.textChanged.connect(self.update_preview)
        self.education_office_edit.textChanged.connect(self.update_preview)

        # أزرار الإجراءات
        buttons_layout = QtWidgets.QHBoxLayout()

        save_btn = QtWidgets.QPushButton('💾 حفظ')
        save_btn.clicked.connect(self.accept)
        save_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)

        cancel_btn = QtWidgets.QPushButton('❌ إلغاء')
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def get_input_style(self):
        return """
            QLineEdit {
                padding: 8px 12px;
                font-size: 14px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #4a90e2;
                outline: none;
            }
        """

    def browse_logo(self):
        """تصفح ملف الشعار"""
        file_path, _ = QtWidgets.QFileDialog.getOpenFileName(
            self, 'اختر شعار الوزارة', '',
            'Image Files (*.png *.jpg *.jpeg *.bmp *.gif)'
        )
        if file_path:
            self.logo_path_edit.setText(file_path)
            self.update_preview()

    def update_preview(self):
        """تحديث معاينة الرأس"""
        school_name = self.school_name_edit.text() or 'اسم المدرسة'
        education_dept = self.education_dept_edit.text() or 'الإدارة العامة للتعليم'
        education_office = self.education_office_edit.text() or 'مكتب التعليم'

        preview_text = f"""
🏛️ {education_dept}
🏢 {education_office}
🏫 {school_name}
        """.strip()

        self.preview_area.setText(preview_text)

    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        if hasattr(self.parent(), 'load_school_settings'):
            settings = self.parent().load_school_settings()
            self.school_name_edit.setText(settings.get('school_name', ''))
            self.education_dept_edit.setText(settings.get('education_department', ''))
            self.education_office_edit.setText(settings.get('education_office', ''))
            self.logo_path_edit.setText(settings.get('logo_path', ''))
            self.update_preview()

    def get_settings(self):
        """الحصول على الإعدادات"""
        return {
            'school_name': self.school_name_edit.text().strip(),
            'education_department': self.education_dept_edit.text().strip(),
            'education_office': self.education_office_edit.text().strip(),
            'logo_path': self.logo_path_edit.text().strip()
        }


if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)

    # تطبيق خط عربي جميل
    font = QtGui.QFont("Arial", 10)
    app.setFont(font)

    win = MainWindow()
    win.show()
    code = app.exec()
    encrypt_and_cleanup()
    sys.exit(code)
