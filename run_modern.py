#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل النظام بالتصميم الحديث
"""

import sys
import os
from PySide6 import QtWidgets, QtCore, QtGui

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    
    # إنشاء التطبيق
    app = QtWidgets.QApplication(sys.argv)
    
    # تعيين خصائص التطبيق
    app.setApplicationName("نظام إدارة الغياب المدرسي")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Ahmed Rabie")
    
    # تطبيق خط عربي مناسب
    try:
        font = QtGui.QFont("Segoe UI", 10)
        app.setFont(font)
    except:
        # في حالة عدم توفر الخط
        font = QtGui.QFont("Arial", 10)
        app.setFont(font)
    
    # تعيين اتجاه النص للعربية
    app.setLayoutDirection(QtCore.Qt.RightToLeft)
    
    # استيراد وتشغيل النافذة الرئيسية
    try:
        from modern_ui import ModernMainWindow
        
        # إنشاء النافذة الرئيسية
        window = ModernMainWindow()
        window.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except ImportError as e:
        # في حالة عدم توفر ملف التصميم الحديث
        QtWidgets.QMessageBox.critical(
            None, "خطأ", 
            f"فشل في تحميل التصميم الحديث:\n{str(e)}\n\nسيتم تشغيل النسخة التقليدية..."
        )
        
        # تشغيل النسخة التقليدية
        from main import MainWindow
        app_window = MainWindow()
        app_window.show()
        sys.exit(app.exec())
        
    except Exception as e:
        # في حالة حدوث خطأ آخر
        QtWidgets.QMessageBox.critical(
            None, "خطأ", 
            f"فشل في تشغيل النظام:\n{str(e)}"
        )
        sys.exit(1)

if __name__ == "__main__":
    main()
