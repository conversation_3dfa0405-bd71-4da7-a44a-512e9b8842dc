@echo off
chcp 65001 >nul
title تثبيت نظام إدارة الغياب المدرسي

echo ===============================================
echo 🎓 تثبيت نظام إدارة الغياب المدرسي المحسن
echo Enhanced School Attendance Management System
echo ===============================================
echo.

echo 🔍 فحص متطلبات النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo.
    echo 💡 يرجى تثبيت Python 3.8 أو أحدث من:
    echo    https://python.org/downloads/
    echo.
    echo 📋 خطوات التثبيت:
    echo    1. اذهب إلى python.org
    echo    2. حمل أحدث إصدار Python
    echo    3. تأكد من تحديد "Add Python to PATH"
    echo    4. أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
python --version

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo 💡 جرب إعادة تثبيت Python مع تحديد "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ pip متوفر
echo.

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ خطأ: ملف requirements.txt غير موجود
    echo 💡 تأكد من وجود جميع ملفات النظام
    echo.
    pause
    exit /b 1
)

echo 📦 تثبيت المكتبات المطلوبة...
echo.

REM ترقية pip
echo 🔧 ترقية pip...
python -m pip install --upgrade pip

REM تثبيت المتطلبات
echo 📥 تثبيت المكتبات...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت بعض المكتبات
    echo.
    echo 💡 جرب الحلول التالية:
    echo    1. تشغيل هذا الملف كمدير ^(Run as Administrator^)
    echo    2. تثبيت المكتبات يدوياً:
    echo       pip install PySide6 pandas openpyxl cryptography
    echo       pip install hijri-converter python-docx
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المكتبات بنجاح!
echo.

REM التحقق من وجود الملفات الأساسية
echo 🔍 التحقق من ملفات النظام...

if not exist "main.py" (
    echo ❌ ملف main.py غير موجود
    set missing_files=1
)

if not exist "config.py" (
    echo ❌ ملف config.py غير موجود
    set missing_files=1
)

if defined missing_files (
    echo.
    echo ❌ بعض الملفات الأساسية مفقودة
    echo 💡 تأكد من وجود جميع ملفات النظام
    echo.
    pause
    exit /b 1
)

echo ✅ جميع الملفات الأساسية موجودة
echo.

echo ===============================================
echo 🎉 تم تثبيت النظام بنجاح!
echo ===============================================
echo.
echo 🚀 لتشغيل النظام:
echo    • انقر مرتين على "تشغيل_النظام.bat"
echo    • أو اكتب في سطر الأوامر: python main.py
echo.
echo 📖 لمزيد من المعلومات راجع ملف README.md
echo.

REM إنشاء اختصار على سطح المكتب
set desktop=%USERPROFILE%\Desktop
if exist "%desktop%" (
    echo 🔗 إنشاء اختصار على سطح المكتب...
    copy "تشغيل_النظام.bat" "%desktop%\نظام الغياب المدرسي.bat" >nul 2>&1
    if not errorlevel 1 (
        echo ✅ تم إنشاء اختصار على سطح المكتب
    )
)

echo.
echo 💡 نصائح مهمة:
echo    • احتفظ بنسخة احتياطية من ملفات النظام
echo    • لا تحذف ملف config.py
echo    • استخدم "📊 استيراد Excel" لإضافة بيانات الطلاب
echo.

pause
