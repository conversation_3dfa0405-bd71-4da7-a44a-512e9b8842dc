#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن لنظام macOS
Enhanced macOS launcher for School Attendance Management System
"""

import sys
import os
import subprocess
from pathlib import Path

def check_and_install_requirements():
    """فحص وتثبيت المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # قائمة المكتبات الأساسية
    basic_requirements = [
        'PySide6',
        'pandas', 
        'openpyxl',
        'cryptography'
    ]
    
    # قائمة المكتبات الاختيارية
    optional_requirements = [
        'hijri-converter',
        'python-docx'
    ]
    
    missing_basic = []
    missing_optional = []
    
    # فحص المكتبات الأساسية
    for package in basic_requirements:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} متوفر")
        except ImportError:
            print(f"❌ {package} غير متوفر")
            missing_basic.append(package)
    
    # فحص المكتبات الاختيارية
    for package in optional_requirements:
        try:
            if package == 'python-docx':
                import docx
            else:
                __import__(package.replace('-', '_'))
            print(f"✅ {package} متوفر")
        except ImportError:
            print(f"⚠️ {package} غير متوفر (اختياري)")
            missing_optional.append(package)
    
    # تثبيت المكتبات المفقودة
    if missing_basic:
        print(f"\n📦 تثبيت المكتبات الأساسية المفقودة...")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '--user'
            ] + missing_basic)
            print("✅ تم تثبيت المكتبات الأساسية")
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت المكتبات الأساسية")
            return False
    
    if missing_optional:
        print(f"\n📦 تثبيت المكتبات الاختيارية...")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '--user'
            ] + missing_optional)
            print("✅ تم تثبيت المكتبات الاختيارية")
        except subprocess.CalledProcessError:
            print("⚠️ فشل في تثبيت بعض المكتبات الاختيارية (لا يؤثر على عمل البرنامج)")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🎓 نظام إدارة الغياب المدرسي المحسن")
    print("Enhanced School Attendance Management System")
    print("=" * 60)
    
    # التحقق من وجود ملف main.py
    if not Path('main.py').exists():
        print("❌ خطأ: ملف main.py غير موجود")
        print("تأكد من وجود جميع ملفات النظام")
        return
    
    # فحص وتثبيت المتطلبات
    if not check_and_install_requirements():
        print("❌ فشل في تحضير المتطلبات")
        return
    
    print("\n🚀 تشغيل النظام...")
    print("=" * 60)
    
    # تعيين متغيرات البيئة لتقليل التحذيرات
    os.environ['QT_MAC_WANTS_LAYER'] = '1'
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    try:
        # تشغيل البرنامج الرئيسي
        import main
        
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\n💡 جرب:")
        print("1. إعادة تشغيل Terminal")
        print("2. تشغيل: python3 -m pip install --upgrade pip")
        print("3. تشغيل: python3 -m pip install --user PySide6 pandas openpyxl cryptography")

if __name__ == '__main__':
    main()
