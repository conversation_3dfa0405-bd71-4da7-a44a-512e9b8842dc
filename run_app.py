#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة الغياب المدرسي المحسن
Enhanced School Attendance Management System Launcher

هذا الملف يوفر طريقة محسنة لتشغيل النظام مع فحص المتطلبات والإعدادات
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ إصدار Python مناسب: {sys.version_info.major}.{sys.version_info.minor}")
    return True

def check_required_packages():
    """فحص المكتبات المطلوبة"""
    required_packages = {
        'PySide6': 'PySide6>=6.5.0',
        'pandas': 'pandas>=1.5.0',
        'openpyxl': 'openpyxl>=3.0.0',
        'xlrd': 'xlrd>=2.0.0',
        'cryptography': 'cryptography>=3.4.0',
        'hijri_converter': 'hijri-converter>=2.2.0',
        'docx': 'python-docx>=0.8.11'
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            if package == 'docx':
                import docx
            else:
                importlib.import_module(package)
            print(f"✅ {package} متوفر")
        except ImportError:
            print(f"❌ {package} غير متوفر")
            missing_packages.append(pip_name)
    
    return missing_packages

def install_missing_packages(packages):
    """تثبيت المكتبات المفقودة"""
    if not packages:
        return True
    
    print(f"\n🔧 سيتم تثبيت المكتبات المفقودة: {', '.join(packages)}")
    response = input("هل تريد المتابعة؟ (y/n): ").lower().strip()
    
    if response not in ['y', 'yes', 'نعم', 'ن']:
        print("❌ تم إلغاء التثبيت")
        return False
    
    try:
        for package in packages:
            print(f"📦 تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ تم تثبيت {package}")
        
        print("✅ تم تثبيت جميع المكتبات بنجاح!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        print("💡 جرب تشغيل الأمر التالي يدوياً:")
        print(f"pip install {' '.join(packages)}")
        return False

def check_config_file():
    """فحص ملف الإعدادات"""
    config_file = current_dir / 'config.py'
    if not config_file.exists():
        print("❌ ملف config.py غير موجود")
        return False
    
    try:
        import config
        # فحص المتغيرات المطلوبة
        required_vars = ['DB_FILE_PLAIN', 'DB_FILE_ENCRYPTED', 'FILE_KEY']
        for var in required_vars:
            if not hasattr(config, var):
                print(f"❌ المتغير {var} غير موجود في config.py")
                return False
        
        print("✅ ملف الإعدادات صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ملف الإعدادات: {e}")
        return False

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب (Windows فقط)"""
    if sys.platform != 'win32':
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, 'نظام الغياب المدرسي.lnk')
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = str(current_dir / 'run_app.py')
        shortcut.WorkingDirectory = str(current_dir)
        shortcut.IconLocation = sys.executable
        shortcut.save()
        
        print(f"✅ تم إنشاء اختصار على سطح المكتب: {shortcut_path}")
        
    except ImportError:
        print("💡 لإنشاء اختصار على سطح المكتب، قم بتثبيت: pip install winshell pywin32")
    except Exception as e:
        print(f"⚠️ لم يتم إنشاء الاختصار: {e}")

def show_welcome_message():
    """عرض رسالة الترحيب"""
    print("=" * 60)
    print("🎓 نظام إدارة الغياب المدرسي المحسن")
    print("Enhanced School Attendance Management System")
    print("=" * 60)
    print("المطور: نظام محسن بتصميم عصري")
    print("الإصدار: 2.0")
    print("=" * 60)

def main():
    """الدالة الرئيسية"""
    show_welcome_message()
    
    # فحص إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # فحص المكتبات المطلوبة
    print("\n🔍 فحص المكتبات المطلوبة...")
    missing_packages = check_required_packages()
    
    if missing_packages:
        if not install_missing_packages(missing_packages):
            input("اضغط Enter للخروج...")
            return
    
    # فحص ملف الإعدادات
    print("\n🔍 فحص ملف الإعدادات...")
    if not check_config_file():
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء اختصار على سطح المكتب
    create_desktop_shortcut()
    
    print("\n🚀 تشغيل النظام...")
    print("=" * 60)
    
    try:
        # تشغيل التطبيق الرئيسي
        import main
        
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\n💡 تأكد من:")
        print("1. تثبيت جميع المكتبات المطلوبة")
        print("2. وجود ملف main.py في نفس المجلد")
        print("3. صحة ملف config.py")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
