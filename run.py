#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن لنظام إدارة الغياب المدرسي
Enhanced launcher for School Attendance Management System
"""

import sys
import os
from pathlib import Path

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🎓 نظام إدارة الغياب المدرسي المحسن")
    print("Enhanced School Attendance Management System")
    print("=" * 60)
    print("الإصدار: 2.0 | تطوير محسن بتصميم عصري")
    print("=" * 60)

def check_files():
    """فحص وجود الملفات المطلوبة"""
    required_files = ['main.py', 'config.py']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   • {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def check_libraries():
    """فحص المكتبات المطلوبة"""
    print("\n🔍 فحص المكتبات المطلوبة...")
    
    libraries = {
        'PySide6': 'واجهة المستخدم الرسومية',
        'pandas': 'معالجة البيانات',
        'openpyxl': 'ملفات Excel',
        'cryptography': 'التشفير'
    }
    
    missing = []
    
    for lib, desc in libraries.items():
        try:
            __import__(lib)
            print(f"✅ {lib} - {desc}")
        except ImportError:
            print(f"❌ {lib} - {desc} (مفقود)")
            missing.append(lib)
    
    if missing:
        print(f"\n📦 مكتبات مفقودة: {', '.join(missing)}")
        print("💡 لتثبيتها:")
        print(f"pip3 install {' '.join(missing)}")
        return False
    
    print("✅ جميع المكتبات متوفرة")
    return True

def setup_environment():
    """إعداد بيئة التشغيل"""
    # تقليل التحذيرات في macOS
    os.environ['QT_MAC_WANTS_LAYER'] = '1'
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    # إضافة المجلد الحالي لمسار Python
    current_dir = str(Path(__file__).parent)
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

def run_application():
    """تشغيل التطبيق الرئيسي"""
    try:
        print("\n🚀 تشغيل النظام...")
        
        # استيراد النظام الرئيسي
        import main
        
        # إنشاء التطبيق
        app = main.QtWidgets.QApplication(sys.argv)
        
        # تطبيق خط عربي جميل
        font = main.QtGui.QFont("Arial", 10)
        app.setFont(font)
        
        # إنشاء النافذة الرئيسية
        window = main.MainWindow()
        window.show()
        
        print("✅ تم تشغيل النظام بنجاح!")
        print("📱 النافذة مفتوحة الآن - يمكنك استخدام النظام")
        print("🔄 لإغلاق النظام، أغلق النافذة أو اضغط Ctrl+C هنا")
        print("=" * 60)
        
        # تشغيل حلقة الأحداث
        exit_code = app.exec()
        
        # تنظيف عند الإغلاق
        main.encrypt_and_cleanup()
        
        print("\n📝 تم إغلاق النظام بنجاح")
        return exit_code
        
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف النظام بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\n💡 حلول مقترحة:")
        print("1. تأكد من تثبيت جميع المكتبات")
        print("2. جرب: pip3 install PySide6 pandas openpyxl cryptography")
        print("3. تأكد من وجود ملف main.py")
        return 1

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص الملفات
    if not check_files():
        input("\nاضغط Enter للخروج...")
        return 1
    
    # فحص المكتبات
    if not check_libraries():
        print("\n🔧 هل تريد محاولة تثبيت المكتبات المفقودة؟ (y/n)")
        response = input("الإجابة: ").lower().strip()
        
        if response in ['y', 'yes', 'نعم', 'ن']:
            print("💡 قم بتشغيل الأمر التالي:")
            print("pip3 install PySide6 pandas openpyxl cryptography")
        
        input("\nاضغط Enter للخروج...")
        return 1
    
    # إعداد البيئة
    setup_environment()
    
    # تشغيل التطبيق
    exit_code = run_application()
    
    return exit_code

if __name__ == '__main__':
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
