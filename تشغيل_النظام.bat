@echo off
chcp 65001 >nul
title نظام إدارة الغياب المدرسي

echo ===============================================
echo 🎓 نظام إدارة الغياب المدرسي المحسن
echo Enhanced School Attendance Management System
echo ===============================================
echo.

echo 🚀 تشغيل النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود الملفات المطلوبة
if not exist "main.py" (
    echo ❌ خطأ: ملف main.py غير موجود
    echo 💡 تأكد من وجود جميع ملفات النظام
    echo.
    pause
    exit /b 1
)

if not exist "config.py" (
    echo ❌ خطأ: ملف config.py غير موجود
    echo 💡 تأكد من وجود جميع ملفات النظام
    echo.
    pause
    exit /b 1
)

REM تشغيل النظام
if exist "run_app.py" (
    echo ✅ تشغيل النظام مع الفحص التلقائي...
    python run_app.py
) else (
    echo ✅ تشغيل النظام...
    python main.py
)

echo.
echo 📝 تم إغلاق النظام
pause
