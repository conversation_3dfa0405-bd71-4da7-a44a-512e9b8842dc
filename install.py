#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التثبيت السريع لنظام إدارة الغياب المدرسي
Quick Installation Script for School Attendance Management System
"""

import sys
import subprocess
import os
from pathlib import Path

def print_header():
    """طباعة رأس التثبيت"""
    print("=" * 70)
    print("🎓 مرحباً بك في نظام إدارة الغياب المدرسي المحسن")
    print("Enhanced School Attendance Management System")
    print("=" * 70)
    print("🚀 سيتم الآن تثبيت النظام وجميع متطلباته...")
    print()

def check_python():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    if sys.version_info < (3, 8):
        print(f"❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ إصدار Python مناسب: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المكتبات المطلوبة...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        # ترقية pip أولاً
        print("🔧 ترقية pip...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
        
        # تثبيت المتطلبات
        print("📥 تثبيت المكتبات...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        
        print("✅ تم تثبيت جميع المكتبات بنجاح!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        print("\n💡 جرب الحلول التالية:")
        print("1. تشغيل الأمر كمدير (Administrator/sudo)")
        print("2. استخدام بيئة افتراضية")
        print("3. تثبيت المكتبات يدوياً:")
        print("   pip install PySide6 pandas openpyxl cryptography hijri-converter python-docx")
        return False

def create_virtual_environment():
    """إنشاء بيئة افتراضية"""
    print("\n🏗️ إنشاء بيئة افتراضية...")
    
    venv_path = Path("venv")
    if venv_path.exists():
        print("⚠️ البيئة الافتراضية موجودة مسبقاً")
        response = input("هل تريد إعادة إنشائها؟ (y/n): ").lower().strip()
        if response not in ['y', 'yes', 'نعم', 'ن']:
            return True
        
        # حذف البيئة القديمة
        import shutil
        shutil.rmtree(venv_path)
    
    try:
        # إنشاء البيئة الافتراضية
        subprocess.check_call([sys.executable, '-m', 'venv', 'venv'])
        print("✅ تم إنشاء البيئة الافتراضية")
        
        # تحديد مسار Python في البيئة الافتراضية
        if sys.platform == 'win32':
            venv_python = venv_path / 'Scripts' / 'python.exe'
            activate_script = venv_path / 'Scripts' / 'activate.bat'
        else:
            venv_python = venv_path / 'bin' / 'python'
            activate_script = venv_path / 'bin' / 'activate'
        
        # ترقية pip في البيئة الافتراضية
        subprocess.check_call([str(venv_python), '-m', 'pip', 'install', '--upgrade', 'pip'])
        
        # تثبيت المتطلبات في البيئة الافتراضية
        subprocess.check_call([str(venv_python), '-m', 'pip', 'install', '-r', 'requirements.txt'])
        
        print("✅ تم تثبيت المكتبات في البيئة الافتراضية")
        
        # إنشاء ملف تشغيل للبيئة الافتراضية
        if sys.platform == 'win32':
            run_script = """@echo off
cd /d "%~dp0"
call venv\\Scripts\\activate
python main.py
pause"""
            with open("run_with_venv.bat", "w", encoding="utf-8") as f:
                f.write(run_script)
            print("✅ تم إنشاء ملف run_with_venv.bat للتشغيل")
        else:
            run_script = """#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
python main.py"""
            with open("run_with_venv.sh", "w", encoding="utf-8") as f:
                f.write(run_script)
            os.chmod("run_with_venv.sh", 0o755)
            print("✅ تم إنشاء ملف run_with_venv.sh للتشغيل")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إنشاء البيئة الافتراضية: {e}")
        return False

def verify_installation():
    """التحقق من صحة التثبيت"""
    print("\n🔍 التحقق من صحة التثبيت...")
    
    required_files = ['main.py', 'config.py', 'requirements.txt']
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ الملف {file} غير موجود")
            return False
        print(f"✅ {file} موجود")
    
    # فحص المكتبات
    try:
        import PySide6
        import pandas
        import openpyxl
        import cryptography
        import hijri_converter
        import docx
        print("✅ جميع المكتبات المطلوبة متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    if sys.platform != 'win32':
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, 'نظام الغياب المدرسي.lnk')
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = str(Path.cwd() / 'run_app.py')
        shortcut.WorkingDirectory = str(Path.cwd())
        shortcut.IconLocation = sys.executable
        shortcut.save()
        
        print(f"✅ تم إنشاء اختصار على سطح المكتب")
        
    except ImportError:
        print("💡 لإنشاء اختصار على سطح المكتب، قم بتثبيت: pip install winshell pywin32")
    except Exception as e:
        print(f"⚠️ لم يتم إنشاء الاختصار: {e}")

def print_success_message():
    """طباعة رسالة النجاح"""
    print("\n" + "=" * 70)
    print("🎉 تم تثبيت النظام بنجاح!")
    print("=" * 70)
    print("🚀 لتشغيل النظام:")
    print("   python run_app.py")
    print()
    print("📖 أو راجع ملف README.md للمزيد من التفاصيل")
    print()
    print("💡 نصائح:")
    print("   • استخدم run_app.py للتشغيل العادي")
    print("   • استخدم run_with_venv للتشغيل مع البيئة الافتراضية")
    print("   • راجع README.md لدليل الاستخدام الكامل")
    print("=" * 70)

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص Python
    if not check_python():
        input("اضغط Enter للخروج...")
        return
    
    # اختيار طريقة التثبيت
    print("🔧 اختر طريقة التثبيت:")
    print("1. تثبيت عادي (في النظام)")
    print("2. تثبيت في بيئة افتراضية (مستحسن)")
    
    while True:
        choice = input("اختر (1 أو 2): ").strip()
        if choice in ['1', '2']:
            break
        print("❌ اختيار غير صحيح، جرب مرة أخرى")
    
    success = False
    
    if choice == '1':
        # تثبيت عادي
        success = install_requirements()
    else:
        # تثبيت في بيئة افتراضية
        success = create_virtual_environment()
    
    if success:
        # التحقق من التثبيت
        if verify_installation():
            # إنشاء اختصار
            create_desktop_shortcut()
            # رسالة النجاح
            print_success_message()
        else:
            print("❌ فشل في التحقق من التثبيت")
    else:
        print("❌ فشل في التثبيت")
    
    input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
