#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel تجريبي لاختبار استيراد البيانات
"""

import pandas as pd

def create_excel_sample():
    """إنشاء ملف Excel تجريبي"""
    
    # بيانات الطلاب التجريبية
    students_data = [
        # الرابع الابتدائي - أ (طلاب جدد)
        {'اسم الطالب': 'سعد محمد أحمد', 'السجل المدني': '1234567920', 'المرحلة': 'الرابع الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'نورا علي حسن', 'السجل المدني': '1234567921', 'المرحلة': 'الرابع الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'عبدالعزيز سالم', 'السجل المدني': '1234567922', 'المرحلة': 'الرابع الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'ريم أحمد محمد', 'السجل المدني': '1234567923', 'المرحلة': 'الرابع الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'فهد عبدالله', 'السجل المدني': '1234567924', 'المرحلة': 'الرابع الابتدائي', 'الفصل': 'أ'},
        
        # الرابع الابتدائي - ب
        {'اسم الطالب': 'لينا حسن علي', 'السجل المدني': '1234567925', 'المرحلة': 'الرابع الابتدائي', 'الفصل': 'ب'},
        {'اسم الطالب': 'ماجد سعد أحمد', 'السجل المدني': '1234567926', 'المرحلة': 'الرابع الابتدائي', 'الفصل': 'ب'},
        {'اسم الطالب': 'دانا محمد سالم', 'السجل المدني': '1234567927', 'المرحلة': 'الرابع الابتدائي', 'الفصل': 'ب'},
        
        # الخامس الابتدائي - أ
        {'اسم الطالب': 'راشد علي محمد', 'السجل المدني': '1234567928', 'المرحلة': 'الخامس الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'شهد أحمد حسن', 'السجل المدني': '1234567929', 'المرحلة': 'الخامس الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'بدر سالم علي', 'السجل المدني': '1234567930', 'المرحلة': 'الخامس الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'جود محمد أحمد', 'السجل المدني': '1234567931', 'المرحلة': 'الخامس الابتدائي', 'الفصل': 'أ'},
        
        # السادس الابتدائي - أ
        {'اسم الطالب': 'تركي عبدالرحمن', 'السجل المدني': '1234567932', 'المرحلة': 'السادس الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'غلا حسن محمد', 'السجل المدني': '1234567933', 'المرحلة': 'السادس الابتدائي', 'الفصل': 'أ'},
        {'اسم الطالب': 'عمار أحمد سعد', 'السجل المدني': '1234567934', 'المرحلة': 'السادس الابتدائي', 'الفصل': 'أ'},
        
        # تحديث بيانات طلاب موجودين (نقل فصل)
        {'اسم الطالب': 'أحمد محمد علي', 'السجل المدني': '1234567890', 'المرحلة': 'الأول الابتدائي', 'الفصل': 'ب'},  # نقل من أ إلى ب
        {'اسم الطالب': 'فاطمة أحمد سالم', 'السجل المدني': '1234567891', 'المرحلة': 'الثاني الابتدائي', 'الفصل': 'أ'},  # ترفيع للثاني
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(students_data)
    
    # حفظ الملف
    excel_filename = 'بيانات_الطلاب_التجريبية.xlsx'
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel التجريبي: {excel_filename}")
    print("📊 البيانات المضافة:")
    print(f"   - {len(students_data)} طالب وطالبة")
    print("   - مراحل من الرابع إلى السادس الابتدائي")
    print("   - تحديث بيانات طلاب موجودين")
    print("🔄 يمكنك استخدام هذا الملف لاختبار استيراد البيانات")
    
    # عرض عينة من البيانات
    print("\n📋 عينة من البيانات:")
    print(df.head().to_string(index=False))

if __name__ == "__main__":
    try:
        create_excel_sample()
    except ImportError:
        print("❌ مكتبة pandas أو openpyxl غير متوفرة")
        print("يرجى تثبيتها باستخدام:")
        print("pip install pandas openpyxl")
    except Exception as e:
        print(f"❌ خطأ: {e}")
